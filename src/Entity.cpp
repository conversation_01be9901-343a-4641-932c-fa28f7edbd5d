#include "Entity.h"
#include "SoundManager.h"

/**
 * Constructor - This is called when you create a new Entity object
 * Example: Entity player(100, 100, 50, RED, 5.0f);
 *
 * The initialization list (after the colon) is a C++ feature that
 * directly initializes member variables - it's more efficient than
 * assigning values inside the constructor body.
 */
Entity::Entity(float startX, float startY, float size, Color entityColor, float moveSpeed, ItemType itemType)
    : x(startX), y(startY), width(size), height(size), color(entityColor),
      originalColor(entityColor), speed(moveSpeed), selected(false),
      isItem(itemType != ItemType::NONE), isPickedUp(false), carrier(nullptr), offsetX(0), offsetY(0),
      itemType(itemType), isEquipped(false), originalSpeed(moveSpeed), equippedShoes(nullptr),
      soundManager(nullptr), lastFootstepTime(0.0f)
{
    // Constructor body - we could do additional setup here if needed
    // For this simple entity, the initialization list handles everything
}

/**
 * Destructor - This is called when the Entity object is destroyed
 * For our simple entity, we don't need to clean up anything special
 * (no dynamic memory allocation, file handles, etc.)
 */
Entity::~Entity()
{
    // Nothing to clean up for this simple entity
}

/**
 * Update method - Called every frame to update the entity's state
 * This is where you would put game logic like:
 * - Animation updates
 * - Physics calculations
 * - AI behavior
 *
 * For now, it's empty, but we'll use it later if needed
 */
void Entity::Update()
{
    // If this is a picked up item, update position to follow carrier
    if (isItem && isPickedUp && carrier != nullptr) {
        if (isEquipped) {
            // Equipped items are positioned differently (closer to the entity)
            x = carrier->GetX() + carrier->GetWidth() * 0.1f;
            y = carrier->GetY() + carrier->GetHeight() * 0.1f;
        } else {
            // Carried items use the original offset
            x = carrier->GetX() + offsetX;
            y = carrier->GetY() + offsetY;
        }
    }

    // Other update logic can go here
}

/**
 * Draw method - Renders the entity on screen using Raylib functions
 * This should be called between BeginDrawing() and EndDrawing()
 */
void Entity::Draw()
{
    // DrawRectangle takes: x, y, width, height, color
    // We cast float to int because DrawRectangle expects integers
    DrawRectangle((int)x, (int)y, (int)width, (int)height, color);

    // Draw selection indicator if this entity is selected
    if (selected) {
        // Draw a bright outline around the selected entity
        DrawRectangleLines((int)x - 2, (int)y - 2, (int)width + 4, (int)height + 4, YELLOW);
        DrawRectangleLines((int)x - 1, (int)y - 1, (int)width + 2, (int)height + 2, YELLOW);
    }

    // Draw equipment indicator if this item is equipped
    if (isItem && isEquipped) {
        // Draw a small glowing border to indicate equipped status
        DrawRectangleLines((int)x - 1, (int)y - 1, (int)width + 2, (int)height + 2, WHITE);
        DrawRectangleLines((int)x, (int)y, (int)width, (int)height, WHITE);
    }

    // Draw speed boost indicator if this entity has equipped shoes
    if (HasEquippedShoes()) {
        // Draw small speed lines next to the entity
        int lineX = (int)(x + width + 2);
        int lineY = (int)(y + height / 2);
        DrawLine(lineX, lineY - 3, lineX + 5, lineY - 3, LIME);
        DrawLine(lineX, lineY, lineX + 7, lineY, LIME);
        DrawLine(lineX, lineY + 3, lineX + 5, lineY + 3, LIME);
    }
}

/**
 * Movement methods - These move the entity in different directions
 * Each method updates the position by the entity's speed value
 */
void Entity::MoveUp()
{
    y -= speed;  // Subtract from y because (0,0) is top-left in screen coordinates
    PlayFootstepSound();
}

void Entity::MoveDown()
{
    y += speed;  // Add to y to move down the screen
    PlayFootstepSound();
}

void Entity::MoveLeft()
{
    x -= speed;  // Subtract from x to move left
    PlayFootstepSound();
}

void Entity::MoveRight()
{
    x += speed;  // Add to x to move right
    PlayFootstepSound();
}

/**
 * Getter methods - These allow other code to read the entity's properties
 * The 'const' keyword means these methods promise not to modify the object
 * This is good practice and allows the methods to be called on const objects
 */
float Entity::GetX() const
{
    return x;
}

float Entity::GetY() const
{
    return y;
}

float Entity::GetWidth() const
{
    return width;
}

float Entity::GetHeight() const
{
    return height;
}

Color Entity::GetColor() const
{
    return color;
}

/**
 * Setter methods - These allow other code to modify the entity's properties
 * It's good practice to provide controlled access to private data
 */
void Entity::SetPosition(float newX, float newY)
{
    x = newX;
    y = newY;
}

void Entity::SetColor(Color newColor)
{
    color = newColor;
}

void Entity::SetSpeed(float newSpeed)
{
    // You could add validation here, e.g., ensure speed is positive
    if (newSpeed >= 0.0f) {
        speed = newSpeed;
    }
}

/**
 * Utility method to check if the entity is completely within screen boundaries
 * Returns true if the entity is fully inside the screen, false otherwise
 */
bool Entity::IsWithinBounds(int screenWidth, int screenHeight)
{
    return (x >= 0 && y >= 0 &&
            x + width <= screenWidth &&
            y + height <= screenHeight);
}

/**
 * Method to keep the entity within screen boundaries
 * This "clamps" the position so the entity can't go off-screen
 */
void Entity::ClampToBounds(int screenWidth, int screenHeight)
{
    // Clamp X position
    if (x < 0) {
        x = 0;
    } else if (x + width > screenWidth) {
        x = screenWidth - width;
    }

    // Clamp Y position
    if (y < 0) {
        y = 0;
    } else if (y + height > screenHeight) {
        y = screenHeight - height;
    }
}

/**
 * Selection-related methods
 */
bool Entity::IsSelected() const
{
    return selected;
}

void Entity::SetSelected(bool isSelected)
{
    selected = isSelected;

    // Update visual appearance based on selection state
    if (selected) {
        // Make the entity brighter when selected
        color.r = (unsigned char)(originalColor.r * 1.3f > 255 ? 255 : originalColor.r * 1.3f);
        color.g = (unsigned char)(originalColor.g * 1.3f > 255 ? 255 : originalColor.g * 1.3f);
        color.b = (unsigned char)(originalColor.b * 1.3f > 255 ? 255 : originalColor.b * 1.3f);
    } else {
        // Restore original color when deselected
        color = originalColor;
    }
}

/**
 * Mouse collision detection methods
 */
bool Entity::IsPointInside(float pointX, float pointY) const
{
    return (pointX >= x && pointX <= x + width &&
            pointY >= y && pointY <= y + height);
}

bool Entity::IsMouseOver() const
{
    Vector2 mousePos = GetMousePosition();
    return IsPointInside(mousePos.x, mousePos.y);
}

bool Entity::IsItem() const
{
    return isItem;
}

bool Entity::IsPickedUp() const
{
    return isPickedUp;
}

void Entity::PickUp(Entity* newCarrier, float xOffset, float yOffset)
{
    if (isItem && !isPickedUp && newCarrier != nullptr) {
        carrier = newCarrier;
        isPickedUp = true;
        offsetX = xOffset;
        offsetY = yOffset;

        // Optional: Change appearance when picked up
        SetColor(YELLOW); // Visual indicator that item is picked up
    }
}

void Entity::Drop()
{
    if (isItem && isPickedUp) {
        // If this item was equipped, unequip it first
        if (isEquipped) {
            Unequip();
        }

        carrier = nullptr;
        isPickedUp = false;

        // Reset to original color
        SetColor(originalColor);
    }
}

// Equipment system methods implementation
ItemType Entity::GetItemType() const
{
    return itemType;
}

bool Entity::IsEquipped() const
{
    return isEquipped;
}

bool Entity::CanEquip() const
{
    // Only shoes can be equipped for now
    return isItem && itemType == ItemType::SHOES;
}

void Entity::Equip(Entity* wearer)
{
    if (!CanEquip() || !isPickedUp || carrier != wearer || wearer == nullptr) {
        return; // Can't equip if not equipable, not picked up, or wrong carrier
    }

    // If the wearer already has shoes equipped, unequip them first
    if (wearer->HasEquippedShoes()) {
        wearer->GetEquippedShoes()->Unequip();
    }

    isEquipped = true;
    wearer->SetEquippedShoes(this);

    // Apply speed boost for shoes (25% increase)
    if (itemType == ItemType::SHOES) {
        float newSpeed = wearer->GetOriginalSpeed() * 1.25f;
        wearer->SetSpeed(newSpeed);
    }

    // Visual feedback - make equipped items more vibrant
    Color equippedColor = originalColor;
    equippedColor.r = (unsigned char)(equippedColor.r * 1.5f > 255 ? 255 : equippedColor.r * 1.5f);
    equippedColor.g = (unsigned char)(equippedColor.g * 1.5f > 255 ? 255 : equippedColor.g * 1.5f);
    equippedColor.b = (unsigned char)(equippedColor.b * 1.5f > 255 ? 255 : equippedColor.b * 1.5f);
    SetColor(equippedColor);
}

void Entity::Unequip()
{
    if (!isEquipped || carrier == nullptr) {
        return; // Nothing to unequip
    }

    Entity* wearer = carrier;
    isEquipped = false;

    // Remove speed boost for shoes
    if (itemType == ItemType::SHOES) {
        wearer->SetSpeed(wearer->GetOriginalSpeed());
    }

    // Remove this item from the wearer's equipment slot
    if (wearer->GetEquippedShoes() == this) {
        wearer->RemoveEquippedShoes();
    }

    // Reset visual appearance to carried state
    SetColor(YELLOW); // Back to carried color
}

bool Entity::HasEquippedShoes() const
{
    return equippedShoes != nullptr;
}

Entity* Entity::GetEquippedShoes() const
{
    return equippedShoes;
}

void Entity::SetEquippedShoes(Entity* shoes)
{
    equippedShoes = shoes;
}

void Entity::RemoveEquippedShoes()
{
    equippedShoes = nullptr;
}

float Entity::GetOriginalSpeed() const
{
    return originalSpeed;
}

void Entity::SetSoundManager(SoundManager* manager)
{
    soundManager = manager;
}

void Entity::PlayFootstepSound()
{
    if (!soundManager || isItem) {
        return; // No sound manager or this is an item (items don't make footsteps)
    }

    // Get current time to prevent too frequent footstep sounds
    float currentTime = GetTime();

    // Only play footstep sound if enough time has passed (prevents rapid-fire sounds)
    if (currentTime - lastFootstepTime < 0.3f) {
        return; // Too soon for another footstep
    }

    // Update last footstep time
    lastFootstepTime = currentTime;

    // Play different footstep sounds based on whether shoes are equipped
    if (HasEquippedShoes()) {
        soundManager->PlaySound("footstep_shoes", soundManager->GetFootstepsVolume() * soundManager->GetMasterVolume());
    } else {
        soundManager->PlaySound("footstep", soundManager->GetFootstepsVolume() * soundManager->GetMasterVolume());
    }
}
