
#include "EntityManager.h"
#include <cmath>  // For sqrt function used in distance calculations

/**
 * Constructor - initializes the entity manager with no entities selected
 */
EntityManager::EntityManager() : selectedEntityIndex(-1)
{
    // Reserve some space in the vector for efficiency
    entities.reserve(10);  // Reserve space for up to 10 entities
}

/**
 * Destructor - cleanup if needed
 */
EntityManager::~EntityManager()
{
    // std::vector automatically cleans up its contents
    // Entity destructor will be called for each entity
}

/**
 * Add an existing entity to the manager
 */
void EntityManager::AddEntity(const Entity& entity)
{
    entities.push_back(entity);
}

/**
 * Create and add a new entity with the given parameters
 */
void EntityManager::AddEntity(float x, float y, float size, Color color, float speed)
{
    entities.emplace_back(x, y, size, color, speed);
}

/**
 * Add a new item entity with the given parameters
 */
void EntityManager::AddItemEntity(float x, float y, float size, Color color)
{
    // Create a new entity that is marked as an item (last parameter = true)
    entities.emplace_back(x, y, size, color, 0.0f, true);
}

/**
 * Select an entity by index
 */
void EntityManager::SelectEntity(int index)
{
    // First, deselect all entities
    DeselectAll();

    // Check if the index is valid
    if (index >= 0 && index < static_cast<int>(entities.size())) {
        selectedEntityIndex = index;
        entities[index].SetSelected(true);
    }
}

/**
 * Select entity based on mouse click
 * Now takes a Camera2D parameter to properly convert screen coordinates to world coordinates
 */
void EntityManager::SelectEntityByMouse(Camera2D camera)
{
    // Only process if mouse button was just pressed (not held)
    if (IsMouseButtonPressed(MOUSE_BUTTON_LEFT)) {
        // Get mouse position in screen coordinates
        Vector2 screenMousePos = GetMousePosition();

        // Convert screen coordinates to world coordinates using the camera
        // This accounts for camera zoom, position, and rotation
        Vector2 worldMousePos = GetScreenToWorld2D(screenMousePos, camera);

        // Check each entity to see if mouse is over it (using world coordinates)
        for (int i = 0; i < static_cast<int>(entities.size()); i++) {
            if (entities[i].IsPointInside(worldMousePos.x, worldMousePos.y)) {
                SelectEntity(i);
                return;  // Exit after selecting the first entity found
            }
        }

        // If we get here, no entity was clicked - deselect all
        DeselectAll();
    }
}

/**
 * Select entity by keyboard number (1 for first entity, 2 for second, etc.)
 */
void EntityManager::SelectEntityByKeyboard(int keyNumber)
{
    int entityIndex = keyNumber - 1;  // Convert 1-based to 0-based index

    if (entityIndex >= 0 && entityIndex < static_cast<int>(entities.size())) {
        SelectEntity(entityIndex);
    }
}

/**
 * Deselect all entities
 */
void EntityManager::DeselectAll()
{
    selectedEntityIndex = -1;

    // Set all entities to not selected
    for (Entity& entity : entities) {
        entity.SetSelected(false);
    }
}

/**
 * Handle all input for entity selection and movement
 * Now takes a Camera2D parameter to properly handle mouse selection with zoom
 */
void EntityManager::HandleInput(Camera2D camera)
{
    // Handle mouse selection (now with camera coordinate conversion)
    SelectEntityByMouse(camera);

    // Handle keyboard selection
    if (IsKeyPressed(KEY_ONE)) {
        SelectEntityByKeyboard(1);
    }
    if (IsKeyPressed(KEY_TWO)) {
        SelectEntityByKeyboard(2);
    }

    // Handle movement for selected entity
    if (HasSelectedEntity()) {
        Entity* selectedEntity = GetSelectedEntity();

        // WASD movement
        if (IsKeyDown(KEY_W) || IsKeyDown(KEY_UP)) {
            selectedEntity->MoveUp();
        }
        if (IsKeyDown(KEY_S) || IsKeyDown(KEY_DOWN)) {
            selectedEntity->MoveDown();
        }
        if (IsKeyDown(KEY_A) || IsKeyDown(KEY_LEFT)) {
            selectedEntity->MoveLeft();
        }
        if (IsKeyDown(KEY_D) || IsKeyDown(KEY_RIGHT)) {
            selectedEntity->MoveRight();
        }
    }

    // Add item pickup/drop controls
    if (HasSelectedEntity()) {
        // Press E to pick up nearby items
        if (IsKeyPressed(KEY_E)) {
            CheckItemPickup();
        }

        // Press Q to drop carried item
        if (IsKeyPressed(KEY_Q)) {
            DropCarriedItem();
        }
    }
}

/**
 * Update all entities
 */
void EntityManager::Update()
{
    for (Entity& entity : entities) {
        entity.Update();
    }
}

/**
 * Draw all entities
 */
void EntityManager::DrawAll()
{
    for (Entity& entity : entities) {
        entity.Draw();
    }
}

/**
 * Utility methods
 */
int EntityManager::GetSelectedEntityIndex() const
{
    return selectedEntityIndex;
}

bool EntityManager::HasSelectedEntity() const
{
    return selectedEntityIndex >= 0 && selectedEntityIndex < static_cast<int>(entities.size());
}

Entity* EntityManager::GetSelectedEntity()
{
    if (HasSelectedEntity()) {
        return &entities[selectedEntityIndex];
    }
    return nullptr;
}

size_t EntityManager::GetEntityCount() const
{
    return entities.size();
}

/**
 * Keep all entities within screen boundaries
 */
void EntityManager::ClampAllToBounds(int screenWidth, int screenHeight)
{
    for (Entity& entity : entities) {
        entity.ClampToBounds(screenWidth, screenHeight);
    }
}

void EntityManager::CheckItemPickup()
{
    // Only proceed if we have a selected entity
    if (!HasSelectedEntity()) return;

    Entity* selectedEntity = GetSelectedEntity();

    // Check all entities to find nearby items
    for (Entity& entity : entities) {
        // Skip if not an item or already picked up
        if (!entity.IsItem() || entity.IsPickedUp()) continue;

        // Calculate distance between selected entity and item
        float dx = selectedEntity->GetX() - entity.GetX();
        float dy = selectedEntity->GetY() - entity.GetY();
        float distance = std::sqrt(dx*dx + dy*dy);

        // If close enough, pick up the item
        if (distance < selectedEntity->GetWidth() + 20) { // Pickup radius
            // Position the item slightly to the right and above the carrier
            entity.PickUp(selectedEntity, selectedEntity->GetWidth(), -10);
            return; // Only pick up one item at a time
        }
    }
}

void EntityManager::DropCarriedItem()
{
    // Only proceed if we have a selected entity
    if (!HasSelectedEntity()) return;

    // Check all entities to find items carried by the selected entity
    for (Entity& entity : entities) {
        if (entity.IsItem() && entity.IsPickedUp()) {
            entity.Drop();
            // Optional: Position the item a bit away from the player
            entity.SetPosition(
                GetSelectedEntity()->GetX() + GetSelectedEntity()->GetWidth() + 10,
                GetSelectedEntity()->GetY()
            );
        }
    }
}
