#include "SoundManager.h"
#include <iostream>

/**
 * Constructor - Initialize the sound manager with default settings
 */
SoundManager::SoundManager() 
    : masterVolume(1.0f), effectsVolume(1.0f), footstepsVolume(0.7f), audioInitialized(false)
{
    // Constructor body - audio initialization happens in InitializeAudio()
}

/**
 * Destructor - Clean up all loaded sounds and audio system
 */
SoundManager::~SoundManager()
{
    CleanupAudio();
}

/**
 * Initialize the audio system
 * Returns true if successful, false otherwise
 */
bool SoundManager::InitializeAudio()
{
    if (audioInitialized) {
        return true; // Already initialized
    }
    
    // Initialize Raylib's audio device
    InitAudioDevice();
    
    // Check if audio device was initialized successfully
    // Note: Raylib doesn't provide a direct way to check this,
    // so we'll assume it worked and set our flag
    audioInitialized = true;
    
    std::cout << "Audio system initialized successfully" << std::endl;
    return true;
}

/**
 * Clean up the audio system and all loaded sounds
 */
void SoundManager::CleanupAudio()
{
    if (!audioInitialized) {
        return; // Nothing to clean up
    }
    
    // Unload all sounds
    UnloadAllSounds();
    
    // Close the audio device
    CloseAudioDevice();
    
    audioInitialized = false;
    std::cout << "Audio system cleaned up" << std::endl;
}

/**
 * Load a sound file and store it with the given name
 * Returns true if successful, false otherwise
 */
bool SoundManager::LoadSound(const std::string& name, const std::string& filename)
{
    if (!audioInitialized) {
        std::cerr << "Audio system not initialized! Call InitializeAudio() first." << std::endl;
        return false;
    }
    
    // Check if sound is already loaded
    if (sounds.find(name) != sounds.end()) {
        std::cout << "Sound '" << name << "' is already loaded" << std::endl;
        return true;
    }
    
    // Load the sound file
    Sound sound = ::LoadSound(filename.c_str());
    
    // Check if loading was successful
    // Note: Raylib's LoadSound doesn't return null on failure,
    // but we can check if the sound data is valid
    if (sound.stream.buffer == nullptr) {
        std::cerr << "Failed to load sound: " << filename << std::endl;
        return false;
    }
    
    // Store the sound in our map
    sounds[name] = sound;
    std::cout << "Loaded sound: " << name << " from " << filename << std::endl;
    return true;
}

/**
 * Unload a specific sound by name
 */
void SoundManager::UnloadSound(const std::string& name)
{
    auto it = sounds.find(name);
    if (it != sounds.end()) {
        ::UnloadSound(it->second);
        sounds.erase(it);
        std::cout << "Unloaded sound: " << name << std::endl;
    }
}

/**
 * Unload all loaded sounds
 */
void SoundManager::UnloadAllSounds()
{
    for (auto& pair : sounds) {
        ::UnloadSound(pair.second);
    }
    sounds.clear();
    std::cout << "Unloaded all sounds" << std::endl;
}

/**
 * Play a sound by name with default volume
 */
void SoundManager::PlaySound(const std::string& name)
{
    PlaySound(name, effectsVolume * masterVolume);
}

/**
 * Play a sound by name with specified volume
 */
void SoundManager::PlaySound(const std::string& name, float volume)
{
    if (!audioInitialized) {
        return; // Audio not initialized
    }
    
    auto it = sounds.find(name);
    if (it != sounds.end()) {
        // Set the volume and play the sound
        SetSoundVolume(it->second, volume);
        ::PlaySound(it->second);
    } else {
        std::cerr << "Sound not found: " << name << std::endl;
    }
}

/**
 * Play a sound only if it's not already playing (prevents overlapping)
 */
void SoundManager::PlaySoundIfNotPlaying(const std::string& name)
{
    if (!IsSoundPlaying(name)) {
        PlaySound(name);
    }
}

/**
 * Set master volume (affects all sounds)
 */
void SoundManager::SetMasterVolume(float volume)
{
    masterVolume = volume < 0.0f ? 0.0f : (volume > 1.0f ? 1.0f : volume);
}

/**
 * Set effects volume
 */
void SoundManager::SetEffectsVolume(float volume)
{
    effectsVolume = volume < 0.0f ? 0.0f : (volume > 1.0f ? 1.0f : volume);
}

/**
 * Set footsteps volume
 */
void SoundManager::SetFootstepsVolume(float volume)
{
    footstepsVolume = volume < 0.0f ? 0.0f : (volume > 1.0f ? 1.0f : volume);
}

/**
 * Getter methods for volume levels
 */
float SoundManager::GetMasterVolume() const { return masterVolume; }
float SoundManager::GetEffectsVolume() const { return effectsVolume; }
float SoundManager::GetFootstepsVolume() const { return footstepsVolume; }

/**
 * Check if a sound is loaded
 */
bool SoundManager::IsSoundLoaded(const std::string& name) const
{
    return sounds.find(name) != sounds.end();
}

/**
 * Check if a sound is currently playing
 */
bool SoundManager::IsSoundPlaying(const std::string& name) const
{
    if (!audioInitialized) {
        return false;
    }
    
    auto it = sounds.find(name);
    if (it != sounds.end()) {
        return ::IsSoundPlaying(it->second);
    }
    return false;
}

/**
 * Check if audio system is initialized
 */
bool SoundManager::IsAudioInitialized() const
{
    return audioInitialized;
}

/**
 * Load all game-specific sounds
 * This method loads all the sounds needed for the equipment system
 */
void SoundManager::LoadGameSounds()
{
    if (!audioInitialized) {
        std::cerr << "Cannot load sounds: Audio system not initialized" << std::endl;
        return;
    }
    
    std::cout << "Loading game sounds..." << std::endl;
    
    // Load equipment sounds
    LoadSound("pickup", "sounds/pickup.wav");
    LoadSound("equip", "sounds/equip.wav");
    LoadSound("unequip", "sounds/unequip.wav");
    LoadSound("drop", "sounds/drop.wav");
    
    // Load movement sounds
    LoadSound("footstep", "sounds/footstep.wav");
    LoadSound("footstep_shoes", "sounds/footstep_shoes.wav");
    
    std::cout << "Game sounds loading complete!" << std::endl;
}
