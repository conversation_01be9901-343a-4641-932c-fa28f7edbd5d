
Defines found: 56

Define 001: <PERSON><PERSON><PERSON><PERSON>_H
  Name: <PERSON><PERSON><PERSON>IB_H
  Type: GUARD
  Value: 
  Description: 
Define 002: RAYLIB_VERSION_MAJOR
  Name: RAY<PERSON>IB_VERSION_MAJOR
  Type: INT
  Value: 4
  Description: 
Define 003: RAYLIB_VERSION_MINOR
  Name: RAYLIB_VERSION_MINOR
  Type: INT
  Value: 5
  Description: 
Define 004: RAYLIB_VERSION_PATCH
  Name: RAYLIB_VERSION_PATCH
  Type: INT
  Value: 0
  Description: 
Define 005: RAYLIB_VERSION
  Name: RAYLIB_VERSION
  Type: STRING
  Value: "4.5"
  Description: 
Define 006: __declspec(x)
  Name: __declspec(x)
  Type: MACRO
  Value: __attribute__((x))
  Description: 
Define 007: RLAPI
  Name: RLAPI
  Type: UNKNOWN
  Value: __declspec(dllexport)
  Description: We are building the library as a Win32 shared library (.dll)
Define 008: PI
  Name: PI
  Type: FLOAT
  Value: 3.14159265358979323846
  Description: 
Define 009: DEG2RAD
  Name: DEG2RAD
  Type: FLOAT_MATH
  Value: (PI/180.0f)
  Description: 
Define 010: RAD2DEG
  Name: RAD2DEG
  Type: FLOAT_MATH
  Value: (180.0f/PI)
  Description: 
Define 011: RL_MALLOC(sz)
  Name: RL_MALLOC(sz)
  Type: MACRO
  Value: malloc(sz)
  Description: 
Define 012: RL_CALLOC(n,sz)
  Name: RL_CALLOC(n,sz)
  Type: MACRO
  Value: calloc(n,sz)
  Description: 
Define 013: RL_REALLOC(ptr,sz)
  Name: RL_REALLOC(ptr,sz)
  Type: MACRO
  Value: realloc(ptr,sz)
  Description: 
Define 014: RL_FREE(ptr)
  Name: RL_FREE(ptr)
  Type: MACRO
  Value: free(ptr)
  Description: 
Define 015: CLITERAL(type)
  Name: CLITERAL(type)
  Type: MACRO
  Value: type
  Description: 
Define 016: RL_COLOR_TYPE
  Name: RL_COLOR_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 017: RL_RECTANGLE_TYPE
  Name: RL_RECTANGLE_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 018: RL_VECTOR2_TYPE
  Name: RL_VECTOR2_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 019: RL_VECTOR3_TYPE
  Name: RL_VECTOR3_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 020: RL_VECTOR4_TYPE
  Name: RL_VECTOR4_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 021: RL_QUATERNION_TYPE
  Name: RL_QUATERNION_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 022: RL_MATRIX_TYPE
  Name: RL_MATRIX_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 023: LIGHTGRAY
  Name: LIGHTGRAY
  Type: COLOR
  Value: CLITERAL(Color){ 200, 200, 200, 255 }
  Description: Light Gray
Define 024: GRAY
  Name: GRAY
  Type: COLOR
  Value: CLITERAL(Color){ 130, 130, 130, 255 }
  Description: Gray
Define 025: DARKGRAY
  Name: DARKGRAY
  Type: COLOR
  Value: CLITERAL(Color){ 80, 80, 80, 255 }
  Description: Dark Gray
Define 026: YELLOW
  Name: YELLOW
  Type: COLOR
  Value: CLITERAL(Color){ 253, 249, 0, 255 }
  Description: Yellow
Define 027: GOLD
  Name: GOLD
  Type: COLOR
  Value: CLITERAL(Color){ 255, 203, 0, 255 }
  Description: Gold
Define 028: ORANGE
  Name: ORANGE
  Type: COLOR
  Value: CLITERAL(Color){ 255, 161, 0, 255 }
  Description: Orange
Define 029: PINK
  Name: PINK
  Type: COLOR
  Value: CLITERAL(Color){ 255, 109, 194, 255 }
  Description: Pink
Define 030: RED
  Name: RED
  Type: COLOR
  Value: CLITERAL(Color){ 230, 41, 55, 255 }
  Description: Red
Define 031: MAROON
  Name: MAROON
  Type: COLOR
  Value: CLITERAL(Color){ 190, 33, 55, 255 }
  Description: Maroon
Define 032: GREEN
  Name: GREEN
  Type: COLOR
  Value: CLITERAL(Color){ 0, 228, 48, 255 }
  Description: Green
Define 033: LIME
  Name: LIME
  Type: COLOR
  Value: CLITERAL(Color){ 0, 158, 47, 255 }
  Description: Lime
Define 034: DARKGREEN
  Name: DARKGREEN
  Type: COLOR
  Value: CLITERAL(Color){ 0, 117, 44, 255 }
  Description: Dark Green
Define 035: SKYBLUE
  Name: SKYBLUE
  Type: COLOR
  Value: CLITERAL(Color){ 102, 191, 255, 255 }
  Description: Sky Blue
Define 036: BLUE
  Name: BLUE
  Type: COLOR
  Value: CLITERAL(Color){ 0, 121, 241, 255 }
  Description: Blue
Define 037: DARKBLUE
  Name: DARKBLUE
  Type: COLOR
  Value: CLITERAL(Color){ 0, 82, 172, 255 }
  Description: Dark Blue
Define 038: PURPLE
  Name: PURPLE
  Type: COLOR
  Value: CLITERAL(Color){ 200, 122, 255, 255 }
  Description: Purple
Define 039: VIOLET
  Name: VIOLET
  Type: COLOR
  Value: CLITERAL(Color){ 135, 60, 190, 255 }
  Description: Violet
Define 040: DARKPURPLE
  Name: DARKPURPLE
  Type: COLOR
  Value: CLITERAL(Color){ 112, 31, 126, 255 }
  Description: Dark Purple
Define 041: BEIGE
  Name: BEIGE
  Type: COLOR
  Value: CLITERAL(Color){ 211, 176, 131, 255 }
  Description: Beige
Define 042: BROWN
  Name: BROWN
  Type: COLOR
  Value: CLITERAL(Color){ 127, 106, 79, 255 }
  Description: Brown
Define 043: DARKBROWN
  Name: DARKBROWN
  Type: COLOR
  Value: CLITERAL(Color){ 76, 63, 47, 255 }
  Description: Dark Brown
Define 044: WHITE
  Name: WHITE
  Type: COLOR
  Value: CLITERAL(Color){ 255, 255, 255, 255 }
  Description: White
Define 045: BLACK
  Name: BLACK
  Type: COLOR
  Value: CLITERAL(Color){ 0, 0, 0, 255 }
  Description: Black
Define 046: BLANK
  Name: BLANK
  Type: COLOR
  Value: CLITERAL(Color){ 0, 0, 0, 0 }
  Description: Blank (Transparent)
Define 047: MAGENTA
  Name: MAGENTA
  Type: COLOR
  Value: CLITERAL(Color){ 255, 0, 255, 255 }
  Description: Magenta
Define 048: RAYWHITE
  Name: RAYWHITE
  Type: COLOR
  Value: CLITERAL(Color){ 245, 245, 245, 255 }
  Description: My own White (raylib logo)
Define 049: RL_BOOL_TYPE
  Name: RL_BOOL_TYPE
  Type: GUARD
  Value: 
  Description: 
Define 050: MOUSE_LEFT_BUTTON
  Name: MOUSE_LEFT_BUTTON
  Type: UNKNOWN
  Value: MOUSE_BUTTON_LEFT
  Description: 
Define 051: MOUSE_RIGHT_BUTTON
  Name: MOUSE_RIGHT_BUTTON
  Type: UNKNOWN
  Value: MOUSE_BUTTON_RIGHT
  Description: 
Define 052: MOUSE_MIDDLE_BUTTON
  Name: MOUSE_MIDDLE_BUTTON
  Type: UNKNOWN
  Value: MOUSE_BUTTON_MIDDLE
  Description: 
Define 053: MATERIAL_MAP_DIFFUSE
  Name: MATERIAL_MAP_DIFFUSE
  Type: UNKNOWN
  Value: MATERIAL_MAP_ALBEDO
  Description: 
Define 054: MATERIAL_MAP_SPECULAR
  Name: MATERIAL_MAP_SPECULAR
  Type: UNKNOWN
  Value: MATERIAL_MAP_METALNESS
  Description: 
Define 055: SHADER_LOC_MAP_DIFFUSE
  Name: SHADER_LOC_MAP_DIFFUSE
  Type: UNKNOWN
  Value: SHADER_LOC_MAP_ALBEDO
  Description: 
Define 056: SHADER_LOC_MAP_SPECULAR
  Name: SHADER_LOC_MAP_SPECULAR
  Type: UNKNOWN
  Value: SHADER_LOC_MAP_METALNESS
  Description: 

Structures found: 32

Struct 01: Vector2 (2 fields)
  Name: Vector2
  Description: Vector2, 2 components
  Field[1]: float x // Vector x component
  Field[2]: float y // Vector y component
Struct 02: Vector3 (3 fields)
  Name: Vector3
  Description: Vector3, 3 components
  Field[1]: float x // Vector x component
  Field[2]: float y // Vector y component
  Field[3]: float z // Vector z component
Struct 03: Vector4 (4 fields)
  Name: Vector4
  Description: Vector4, 4 components
  Field[1]: float x // Vector x component
  Field[2]: float y // Vector y component
  Field[3]: float z // Vector z component
  Field[4]: float w // Vector w component
Struct 04: Matrix (16 fields)
  Name: Matrix
  Description: Matrix, 4x4 components, column major, OpenGL style, right-handed
  Field[1]: float m0 // Matrix first row (4 components)
  Field[2]: float m4 // Matrix first row (4 components)
  Field[3]: float m8 // Matrix first row (4 components)
  Field[4]: float m12 // Matrix first row (4 components)
  Field[5]: float m1 // Matrix second row (4 components)
  Field[6]: float m5 // Matrix second row (4 components)
  Field[7]: float m9 // Matrix second row (4 components)
  Field[8]: float m13 // Matrix second row (4 components)
  Field[9]: float m2 // Matrix third row (4 components)
  Field[10]: float m6 // Matrix third row (4 components)
  Field[11]: float m10 // Matrix third row (4 components)
  Field[12]: float m14 // Matrix third row (4 components)
  Field[13]: float m3 // Matrix fourth row (4 components)
  Field[14]: float m7 // Matrix fourth row (4 components)
  Field[15]: float m11 // Matrix fourth row (4 components)
  Field[16]: float m15 // Matrix fourth row (4 components)
Struct 05: Color (4 fields)
  Name: Color
  Description: Color, 4 components, R8G8B8A8 (32bit)
  Field[1]: unsigned char r // Color red value
  Field[2]: unsigned char g // Color green value
  Field[3]: unsigned char b // Color blue value
  Field[4]: unsigned char a // Color alpha value
Struct 06: Rectangle (4 fields)
  Name: Rectangle
  Description: Rectangle, 4 components
  Field[1]: float x // Rectangle top-left corner position x
  Field[2]: float y // Rectangle top-left corner position y
  Field[3]: float width // Rectangle width
  Field[4]: float height // Rectangle height
Struct 07: Image (5 fields)
  Name: Image
  Description: Image, pixel data stored in CPU memory (RAM)
  Field[1]: void * data // Image raw data
  Field[2]: int width // Image base width
  Field[3]: int height // Image base height
  Field[4]: int mipmaps // Mipmap levels, 1 by default
  Field[5]: int format // Data format (PixelFormat type)
Struct 08: Texture (5 fields)
  Name: Texture
  Description: Texture, tex data stored in GPU memory (VRAM)
  Field[1]: unsigned int id // OpenGL texture id
  Field[2]: int width // Texture base width
  Field[3]: int height // Texture base height
  Field[4]: int mipmaps // Mipmap levels, 1 by default
  Field[5]: int format // Data format (PixelFormat type)
Struct 09: RenderTexture (3 fields)
  Name: RenderTexture
  Description: RenderTexture, fbo for texture rendering
  Field[1]: unsigned int id // OpenGL framebuffer object id
  Field[2]: Texture texture // Color buffer attachment texture
  Field[3]: Texture depth // Depth buffer attachment texture
Struct 10: NPatchInfo (6 fields)
  Name: NPatchInfo
  Description: NPatchInfo, n-patch layout info
  Field[1]: Rectangle source // Texture source rectangle
  Field[2]: int left // Left border offset
  Field[3]: int top // Top border offset
  Field[4]: int right // Right border offset
  Field[5]: int bottom // Bottom border offset
  Field[6]: int layout // Layout of the n-patch: 3x3, 1x3 or 3x1
Struct 11: GlyphInfo (5 fields)
  Name: GlyphInfo
  Description: GlyphInfo, font characters glyphs info
  Field[1]: int value // Character value (Unicode)
  Field[2]: int offsetX // Character offset X when drawing
  Field[3]: int offsetY // Character offset Y when drawing
  Field[4]: int advanceX // Character advance position X
  Field[5]: Image image // Character image data
Struct 12: Font (6 fields)
  Name: Font
  Description: Font, font texture and GlyphInfo array data
  Field[1]: int baseSize // Base size (default chars height)
  Field[2]: int glyphCount // Number of glyph characters
  Field[3]: int glyphPadding // Padding around the glyph characters
  Field[4]: Texture2D texture // Texture atlas containing the glyphs
  Field[5]: Rectangle * recs // Rectangles in texture for the glyphs
  Field[6]: GlyphInfo * glyphs // Glyphs info data
Struct 13: Camera3D (5 fields)
  Name: Camera3D
  Description: Camera, defines position/orientation in 3d space
  Field[1]: Vector3 position // Camera position
  Field[2]: Vector3 target // Camera target it looks-at
  Field[3]: Vector3 up // Camera up vector (rotation over its axis)
  Field[4]: float fovy // Camera field-of-view aperture in Y (degrees) in perspective, used as near plane width in orthographic
  Field[5]: int projection // Camera projection: CAMERA_PERSPECTIVE or CAMERA_ORTHOGRAPHIC
Struct 14: Camera2D (4 fields)
  Name: Camera2D
  Description: Camera2D, defines position/orientation in 2d space
  Field[1]: Vector2 offset // Camera offset (displacement from target)
  Field[2]: Vector2 target // Camera target (rotation and zoom origin)
  Field[3]: float rotation // Camera rotation in degrees
  Field[4]: float zoom // Camera zoom (scaling), should be 1.0f by default
Struct 15: Mesh (15 fields)
  Name: Mesh
  Description: Mesh, vertex data and vao/vbo
  Field[1]: int vertexCount // Number of vertices stored in arrays
  Field[2]: int triangleCount // Number of triangles stored (indexed or not)
  Field[3]: float * vertices // Vertex position (XYZ - 3 components per vertex) (shader-location = 0)
  Field[4]: float * texcoords // Vertex texture coordinates (UV - 2 components per vertex) (shader-location = 1)
  Field[5]: float * texcoords2 // Vertex texture second coordinates (UV - 2 components per vertex) (shader-location = 5)
  Field[6]: float * normals // Vertex normals (XYZ - 3 components per vertex) (shader-location = 2)
  Field[7]: float * tangents // Vertex tangents (XYZW - 4 components per vertex) (shader-location = 4)
  Field[8]: unsigned char * colors // Vertex colors (RGBA - 4 components per vertex) (shader-location = 3)
  Field[9]: unsigned short * indices // Vertex indices (in case vertex data comes indexed)
  Field[10]: float * animVertices // Animated vertex positions (after bones transformations)
  Field[11]: float * animNormals // Animated normals (after bones transformations)
  Field[12]: unsigned char * boneIds // Vertex bone ids, max 255 bone ids, up to 4 bones influence by vertex (skinning)
  Field[13]: float * boneWeights // Vertex bone weight, up to 4 bones influence by vertex (skinning)
  Field[14]: unsigned int vaoId // OpenGL Vertex Array Object id
  Field[15]: unsigned int * vboId // OpenGL Vertex Buffer Objects id (default vertex data)
Struct 16: Shader (2 fields)
  Name: Shader
  Description: Shader
  Field[1]: unsigned int id // Shader program id
  Field[2]: int * locs // Shader locations array (RL_MAX_SHADER_LOCATIONS)
Struct 17: MaterialMap (3 fields)
  Name: MaterialMap
  Description: MaterialMap
  Field[1]: Texture2D texture // Material map texture
  Field[2]: Color color // Material map color
  Field[3]: float value // Material map value
Struct 18: Material (3 fields)
  Name: Material
  Description: Material, includes shader and maps
  Field[1]: Shader shader // Material shader
  Field[2]: MaterialMap * maps // Material maps array (MAX_MATERIAL_MAPS)
  Field[3]: float[4] params // Material generic parameters (if required)
Struct 19: Transform (3 fields)
  Name: Transform
  Description: Transform, vertex transformation data
  Field[1]: Vector3 translation // Translation
  Field[2]: Quaternion rotation // Rotation
  Field[3]: Vector3 scale // Scale
Struct 20: BoneInfo (2 fields)
  Name: BoneInfo
  Description: Bone, skeletal animation bone
  Field[1]: char[32] name // Bone name
  Field[2]: int parent // Bone parent
Struct 21: Model (9 fields)
  Name: Model
  Description: Model, meshes, materials and animation data
  Field[1]: Matrix transform // Local transform matrix
  Field[2]: int meshCount // Number of meshes
  Field[3]: int materialCount // Number of materials
  Field[4]: Mesh * meshes // Meshes array
  Field[5]: Material * materials // Materials array
  Field[6]: int * meshMaterial // Mesh material number
  Field[7]: int boneCount // Number of bones
  Field[8]: BoneInfo * bones // Bones information (skeleton)
  Field[9]: Transform * bindPose // Bones base transformation (pose)
Struct 22: ModelAnimation (4 fields)
  Name: ModelAnimation
  Description: ModelAnimation
  Field[1]: int boneCount // Number of bones
  Field[2]: int frameCount // Number of animation frames
  Field[3]: BoneInfo * bones // Bones information (skeleton)
  Field[4]: Transform ** framePoses // Poses array by frame
Struct 23: Ray (2 fields)
  Name: Ray
  Description: Ray, ray for raycasting
  Field[1]: Vector3 position // Ray position (origin)
  Field[2]: Vector3 direction // Ray direction
Struct 24: RayCollision (4 fields)
  Name: RayCollision
  Description: RayCollision, ray hit information
  Field[1]: bool hit // Did the ray hit something?
  Field[2]: float distance // Distance to the nearest hit
  Field[3]: Vector3 point // Point of the nearest hit
  Field[4]: Vector3 normal // Surface normal of hit
Struct 25: BoundingBox (2 fields)
  Name: BoundingBox
  Description: BoundingBox
  Field[1]: Vector3 min // Minimum vertex box-corner
  Field[2]: Vector3 max // Maximum vertex box-corner
Struct 26: Wave (5 fields)
  Name: Wave
  Description: Wave, audio wave data
  Field[1]: unsigned int frameCount // Total number of frames (considering channels)
  Field[2]: unsigned int sampleRate // Frequency (samples per second)
  Field[3]: unsigned int sampleSize // Bit depth (bits per sample): 8, 16, 32 (24 not supported)
  Field[4]: unsigned int channels // Number of channels (1-mono, 2-stereo, ...)
  Field[5]: void * data // Buffer data pointer
Struct 27: AudioStream (5 fields)
  Name: AudioStream
  Description: AudioStream, custom audio stream
  Field[1]: rAudioBuffer * buffer // Pointer to internal data used by the audio system
  Field[2]: rAudioProcessor * processor // Pointer to internal data processor, useful for audio effects
  Field[3]: unsigned int sampleRate // Frequency (samples per second)
  Field[4]: unsigned int sampleSize // Bit depth (bits per sample): 8, 16, 32 (24 not supported)
  Field[5]: unsigned int channels // Number of channels (1-mono, 2-stereo, ...)
Struct 28: Sound (2 fields)
  Name: Sound
  Description: Sound
  Field[1]: AudioStream stream // Audio stream
  Field[2]: unsigned int frameCount // Total number of frames (considering channels)
Struct 29: Music (5 fields)
  Name: Music
  Description: Music, audio stream, anything longer than ~10 seconds should be streamed
  Field[1]: AudioStream stream // Audio stream
  Field[2]: unsigned int frameCount // Total number of frames (considering channels)
  Field[3]: bool looping // Music looping enable
  Field[4]: int ctxType // Type of music context (audio filetype)
  Field[5]: void * ctxData // Audio context data, depends on type
Struct 30: VrDeviceInfo (10 fields)
  Name: VrDeviceInfo
  Description: VrDeviceInfo, Head-Mounted-Display device parameters
  Field[1]: int hResolution // Horizontal resolution in pixels
  Field[2]: int vResolution // Vertical resolution in pixels
  Field[3]: float hScreenSize // Horizontal size in meters
  Field[4]: float vScreenSize // Vertical size in meters
  Field[5]: float vScreenCenter // Screen center in meters
  Field[6]: float eyeToScreenDistance // Distance between eye and display in meters
  Field[7]: float lensSeparationDistance // Lens separation distance in meters
  Field[8]: float interpupillaryDistance // IPD (distance between pupils) in meters
  Field[9]: float[4] lensDistortionValues // Lens distortion constant parameters
  Field[10]: float[4] chromaAbCorrection // Chromatic aberration correction parameters
Struct 31: VrStereoConfig (8 fields)
  Name: VrStereoConfig
  Description: VrStereoConfig, VR stereo rendering configuration for simulator
  Field[1]: Matrix[2] projection // VR projection matrices (per eye)
  Field[2]: Matrix[2] viewOffset // VR view offset matrices (per eye)
  Field[3]: float[2] leftLensCenter // VR left lens center
  Field[4]: float[2] rightLensCenter // VR right lens center
  Field[5]: float[2] leftScreenCenter // VR left screen center
  Field[6]: float[2] rightScreenCenter // VR right screen center
  Field[7]: float[2] scale // VR distortion scale
  Field[8]: float[2] scaleIn // VR distortion scale in
Struct 32: FilePathList (3 fields)
  Name: FilePathList
  Description: File path list
  Field[1]: unsigned int capacity // Filepaths max entries
  Field[2]: unsigned int count // Filepaths entries count
  Field[3]: char ** paths // Filepaths entries

Aliases found: 5

Alias 001: Quaternion
  Type: Vector4
  Name: Quaternion
  Description: Quaternion, 4 components (Vector4 alias)
Alias 002: Texture2D
  Type: Texture
  Name: Texture2D
  Description: Texture2D, same as Texture
Alias 003: TextureCubemap
  Type: Texture
  Name: TextureCubemap
  Description: TextureCubemap, same as Texture
Alias 004: RenderTexture2D
  Type: RenderTexture
  Name: RenderTexture2D
  Description: RenderTexture2D, same as RenderTexture
Alias 005: Camera
  Type: Camera3D
  Name: Camera
  Description: Camera type fallback, defaults to Camera3D

Enums found: 21

Enum 01: ConfigFlags (15 values)
  Name: ConfigFlags
  Description: System/Window config flags
  Value[FLAG_VSYNC_HINT]: 64
  Value[FLAG_FULLSCREEN_MODE]: 2
  Value[FLAG_WINDOW_RESIZABLE]: 4
  Value[FLAG_WINDOW_UNDECORATED]: 8
  Value[FLAG_WINDOW_HIDDEN]: 128
  Value[FLAG_WINDOW_MINIMIZED]: 512
  Value[FLAG_WINDOW_MAXIMIZED]: 1024
  Value[FLAG_WINDOW_UNFOCUSED]: 2048
  Value[FLAG_WINDOW_TOPMOST]: 4096
  Value[FLAG_WINDOW_ALWAYS_RUN]: 256
  Value[FLAG_WINDOW_TRANSPARENT]: 16
  Value[FLAG_WINDOW_HIGHDPI]: 8192
  Value[FLAG_WINDOW_MOUSE_PASSTHROUGH]: 16384
  Value[FLAG_MSAA_4X_HINT]: 32
  Value[FLAG_INTERLACED_HINT]: 65536
Enum 02: TraceLogLevel (8 values)
  Name: TraceLogLevel
  Description: Trace log level
  Value[LOG_ALL]: 0
  Value[LOG_TRACE]: 1
  Value[LOG_DEBUG]: 2
  Value[LOG_INFO]: 3
  Value[LOG_WARNING]: 4
  Value[LOG_ERROR]: 5
  Value[LOG_FATAL]: 6
  Value[LOG_NONE]: 7
Enum 03: KeyboardKey (110 values)
  Name: KeyboardKey
  Description: Keyboard keys (US keyboard layout)
  Value[KEY_NULL]: 0
  Value[KEY_APOSTROPHE]: 39
  Value[KEY_COMMA]: 44
  Value[KEY_MINUS]: 45
  Value[KEY_PERIOD]: 46
  Value[KEY_SLASH]: 47
  Value[KEY_ZERO]: 48
  Value[KEY_ONE]: 49
  Value[KEY_TWO]: 50
  Value[KEY_THREE]: 51
  Value[KEY_FOUR]: 52
  Value[KEY_FIVE]: 53
  Value[KEY_SIX]: 54
  Value[KEY_SEVEN]: 55
  Value[KEY_EIGHT]: 56
  Value[KEY_NINE]: 57
  Value[KEY_SEMICOLON]: 59
  Value[KEY_EQUAL]: 61
  Value[KEY_A]: 65
  Value[KEY_B]: 66
  Value[KEY_C]: 67
  Value[KEY_D]: 68
  Value[KEY_E]: 69
  Value[KEY_F]: 70
  Value[KEY_G]: 71
  Value[KEY_H]: 72
  Value[KEY_I]: 73
  Value[KEY_J]: 74
  Value[KEY_K]: 75
  Value[KEY_L]: 76
  Value[KEY_M]: 77
  Value[KEY_N]: 78
  Value[KEY_O]: 79
  Value[KEY_P]: 80
  Value[KEY_Q]: 81
  Value[KEY_R]: 82
  Value[KEY_S]: 83
  Value[KEY_T]: 84
  Value[KEY_U]: 85
  Value[KEY_V]: 86
  Value[KEY_W]: 87
  Value[KEY_X]: 88
  Value[KEY_Y]: 89
  Value[KEY_Z]: 90
  Value[KEY_LEFT_BRACKET]: 91
  Value[KEY_BACKSLASH]: 92
  Value[KEY_RIGHT_BRACKET]: 93
  Value[KEY_GRAVE]: 96
  Value[KEY_SPACE]: 32
  Value[KEY_ESCAPE]: 256
  Value[KEY_ENTER]: 257
  Value[KEY_TAB]: 258
  Value[KEY_BACKSPACE]: 259
  Value[KEY_INSERT]: 260
  Value[KEY_DELETE]: 261
  Value[KEY_RIGHT]: 262
  Value[KEY_LEFT]: 263
  Value[KEY_DOWN]: 264
  Value[KEY_UP]: 265
  Value[KEY_PAGE_UP]: 266
  Value[KEY_PAGE_DOWN]: 267
  Value[KEY_HOME]: 268
  Value[KEY_END]: 269
  Value[KEY_CAPS_LOCK]: 280
  Value[KEY_SCROLL_LOCK]: 281
  Value[KEY_NUM_LOCK]: 282
  Value[KEY_PRINT_SCREEN]: 283
  Value[KEY_PAUSE]: 284
  Value[KEY_F1]: 290
  Value[KEY_F2]: 291
  Value[KEY_F3]: 292
  Value[KEY_F4]: 293
  Value[KEY_F5]: 294
  Value[KEY_F6]: 295
  Value[KEY_F7]: 296
  Value[KEY_F8]: 297
  Value[KEY_F9]: 298
  Value[KEY_F10]: 299
  Value[KEY_F11]: 300
  Value[KEY_F12]: 301
  Value[KEY_LEFT_SHIFT]: 340
  Value[KEY_LEFT_CONTROL]: 341
  Value[KEY_LEFT_ALT]: 342
  Value[KEY_LEFT_SUPER]: 343
  Value[KEY_RIGHT_SHIFT]: 344
  Value[KEY_RIGHT_CONTROL]: 345
  Value[KEY_RIGHT_ALT]: 346
  Value[KEY_RIGHT_SUPER]: 347
  Value[KEY_KB_MENU]: 348
  Value[KEY_KP_0]: 320
  Value[KEY_KP_1]: 321
  Value[KEY_KP_2]: 322
  Value[KEY_KP_3]: 323
  Value[KEY_KP_4]: 324
  Value[KEY_KP_5]: 325
  Value[KEY_KP_6]: 326
  Value[KEY_KP_7]: 327
  Value[KEY_KP_8]: 328
  Value[KEY_KP_9]: 329
  Value[KEY_KP_DECIMAL]: 330
  Value[KEY_KP_DIVIDE]: 331
  Value[KEY_KP_MULTIPLY]: 332
  Value[KEY_KP_SUBTRACT]: 333
  Value[KEY_KP_ADD]: 334
  Value[KEY_KP_ENTER]: 335
  Value[KEY_KP_EQUAL]: 336
  Value[KEY_BACK]: 4
  Value[KEY_MENU]: 82
  Value[KEY_VOLUME_UP]: 24
  Value[KEY_VOLUME_DOWN]: 25
Enum 04: MouseButton (7 values)
  Name: MouseButton
  Description: Mouse buttons
  Value[MOUSE_BUTTON_LEFT]: 0
  Value[MOUSE_BUTTON_RIGHT]: 1
  Value[MOUSE_BUTTON_MIDDLE]: 2
  Value[MOUSE_BUTTON_SIDE]: 3
  Value[MOUSE_BUTTON_EXTRA]: 4
  Value[MOUSE_BUTTON_FORWARD]: 5
  Value[MOUSE_BUTTON_BACK]: 6
Enum 05: MouseCursor (11 values)
  Name: MouseCursor
  Description: Mouse cursor
  Value[MOUSE_CURSOR_DEFAULT]: 0
  Value[MOUSE_CURSOR_ARROW]: 1
  Value[MOUSE_CURSOR_IBEAM]: 2
  Value[MOUSE_CURSOR_CROSSHAIR]: 3
  Value[MOUSE_CURSOR_POINTING_HAND]: 4
  Value[MOUSE_CURSOR_RESIZE_EW]: 5
  Value[MOUSE_CURSOR_RESIZE_NS]: 6
  Value[MOUSE_CURSOR_RESIZE_NWSE]: 7
  Value[MOUSE_CURSOR_RESIZE_NESW]: 8
  Value[MOUSE_CURSOR_RESIZE_ALL]: 9
  Value[MOUSE_CURSOR_NOT_ALLOWED]: 10
Enum 06: GamepadButton (18 values)
  Name: GamepadButton
  Description: Gamepad buttons
  Value[GAMEPAD_BUTTON_UNKNOWN]: 0
  Value[GAMEPAD_BUTTON_LEFT_FACE_UP]: 1
  Value[GAMEPAD_BUTTON_LEFT_FACE_RIGHT]: 2
  Value[GAMEPAD_BUTTON_LEFT_FACE_DOWN]: 3
  Value[GAMEPAD_BUTTON_LEFT_FACE_LEFT]: 4
  Value[GAMEPAD_BUTTON_RIGHT_FACE_UP]: 5
  Value[GAMEPAD_BUTTON_RIGHT_FACE_RIGHT]: 6
  Value[GAMEPAD_BUTTON_RIGHT_FACE_DOWN]: 7
  Value[GAMEPAD_BUTTON_RIGHT_FACE_LEFT]: 8
  Value[GAMEPAD_BUTTON_LEFT_TRIGGER_1]: 9
  Value[GAMEPAD_BUTTON_LEFT_TRIGGER_2]: 10
  Value[GAMEPAD_BUTTON_RIGHT_TRIGGER_1]: 11
  Value[GAMEPAD_BUTTON_RIGHT_TRIGGER_2]: 12
  Value[GAMEPAD_BUTTON_MIDDLE_LEFT]: 13
  Value[GAMEPAD_BUTTON_MIDDLE]: 14
  Value[GAMEPAD_BUTTON_MIDDLE_RIGHT]: 15
  Value[GAMEPAD_BUTTON_LEFT_THUMB]: 16
  Value[GAMEPAD_BUTTON_RIGHT_THUMB]: 17
Enum 07: GamepadAxis (6 values)
  Name: GamepadAxis
  Description: Gamepad axis
  Value[GAMEPAD_AXIS_LEFT_X]: 0
  Value[GAMEPAD_AXIS_LEFT_Y]: 1
  Value[GAMEPAD_AXIS_RIGHT_X]: 2
  Value[GAMEPAD_AXIS_RIGHT_Y]: 3
  Value[GAMEPAD_AXIS_LEFT_TRIGGER]: 4
  Value[GAMEPAD_AXIS_RIGHT_TRIGGER]: 5
Enum 08: MaterialMapIndex (11 values)
  Name: MaterialMapIndex
  Description: Material map index
  Value[MATERIAL_MAP_ALBEDO]: 0
  Value[MATERIAL_MAP_METALNESS]: 1
  Value[MATERIAL_MAP_NORMAL]: 2
  Value[MATERIAL_MAP_ROUGHNESS]: 3
  Value[MATERIAL_MAP_OCCLUSION]: 4
  Value[MATERIAL_MAP_EMISSION]: 5
  Value[MATERIAL_MAP_HEIGHT]: 6
  Value[MATERIAL_MAP_CUBEMAP]: 7
  Value[MATERIAL_MAP_IRRADIANCE]: 8
  Value[MATERIAL_MAP_PREFILTER]: 9
  Value[MATERIAL_MAP_BRDF]: 10
Enum 09: ShaderLocationIndex (26 values)
  Name: ShaderLocationIndex
  Description: Shader location index
  Value[SHADER_LOC_VERTEX_POSITION]: 0
  Value[SHADER_LOC_VERTEX_TEXCOORD01]: 1
  Value[SHADER_LOC_VERTEX_TEXCOORD02]: 2
  Value[SHADER_LOC_VERTEX_NORMAL]: 3
  Value[SHADER_LOC_VERTEX_TANGENT]: 4
  Value[SHADER_LOC_VERTEX_COLOR]: 5
  Value[SHADER_LOC_MATRIX_MVP]: 6
  Value[SHADER_LOC_MATRIX_VIEW]: 7
  Value[SHADER_LOC_MATRIX_PROJECTION]: 8
  Value[SHADER_LOC_MATRIX_MODEL]: 9
  Value[SHADER_LOC_MATRIX_NORMAL]: 10
  Value[SHADER_LOC_VECTOR_VIEW]: 11
  Value[SHADER_LOC_COLOR_DIFFUSE]: 12
  Value[SHADER_LOC_COLOR_SPECULAR]: 13
  Value[SHADER_LOC_COLOR_AMBIENT]: 14
  Value[SHADER_LOC_MAP_ALBEDO]: 15
  Value[SHADER_LOC_MAP_METALNESS]: 16
  Value[SHADER_LOC_MAP_NORMAL]: 17
  Value[SHADER_LOC_MAP_ROUGHNESS]: 18
  Value[SHADER_LOC_MAP_OCCLUSION]: 19
  Value[SHADER_LOC_MAP_EMISSION]: 20
  Value[SHADER_LOC_MAP_HEIGHT]: 21
  Value[SHADER_LOC_MAP_CUBEMAP]: 22
  Value[SHADER_LOC_MAP_IRRADIANCE]: 23
  Value[SHADER_LOC_MAP_PREFILTER]: 24
  Value[SHADER_LOC_MAP_BRDF]: 25
Enum 10: ShaderUniformDataType (9 values)
  Name: ShaderUniformDataType
  Description: Shader uniform data type
  Value[SHADER_UNIFORM_FLOAT]: 0
  Value[SHADER_UNIFORM_VEC2]: 1
  Value[SHADER_UNIFORM_VEC3]: 2
  Value[SHADER_UNIFORM_VEC4]: 3
  Value[SHADER_UNIFORM_INT]: 4
  Value[SHADER_UNIFORM_IVEC2]: 5
  Value[SHADER_UNIFORM_IVEC3]: 6
  Value[SHADER_UNIFORM_IVEC4]: 7
  Value[SHADER_UNIFORM_SAMPLER2D]: 8
Enum 11: ShaderAttributeDataType (4 values)
  Name: ShaderAttributeDataType
  Description: Shader attribute data types
  Value[SHADER_ATTRIB_FLOAT]: 0
  Value[SHADER_ATTRIB_VEC2]: 1
  Value[SHADER_ATTRIB_VEC3]: 2
  Value[SHADER_ATTRIB_VEC4]: 3
Enum 12: PixelFormat (21 values)
  Name: PixelFormat
  Description: Pixel formats
  Value[PIXELFORMAT_UNCOMPRESSED_GRAYSCALE]: 1
  Value[PIXELFORMAT_UNCOMPRESSED_GRAY_ALPHA]: 2
  Value[PIXELFORMAT_UNCOMPRESSED_R5G6B5]: 3
  Value[PIXELFORMAT_UNCOMPRESSED_R8G8B8]: 4
  Value[PIXELFORMAT_UNCOMPRESSED_R5G5B5A1]: 5
  Value[PIXELFORMAT_UNCOMPRESSED_R4G4B4A4]: 6
  Value[PIXELFORMAT_UNCOMPRESSED_R8G8B8A8]: 7
  Value[PIXELFORMAT_UNCOMPRESSED_R32]: 8
  Value[PIXELFORMAT_UNCOMPRESSED_R32G32B32]: 9
  Value[PIXELFORMAT_UNCOMPRESSED_R32G32B32A32]: 10
  Value[PIXELFORMAT_COMPRESSED_DXT1_RGB]: 11
  Value[PIXELFORMAT_COMPRESSED_DXT1_RGBA]: 12
  Value[PIXELFORMAT_COMPRESSED_DXT3_RGBA]: 13
  Value[PIXELFORMAT_COMPRESSED_DXT5_RGBA]: 14
  Value[PIXELFORMAT_COMPRESSED_ETC1_RGB]: 15
  Value[PIXELFORMAT_COMPRESSED_ETC2_RGB]: 16
  Value[PIXELFORMAT_COMPRESSED_ETC2_EAC_RGBA]: 17
  Value[PIXELFORMAT_COMPRESSED_PVRT_RGB]: 18
  Value[PIXELFORMAT_COMPRESSED_PVRT_RGBA]: 19
  Value[PIXELFORMAT_COMPRESSED_ASTC_4x4_RGBA]: 20
  Value[PIXELFORMAT_COMPRESSED_ASTC_8x8_RGBA]: 21
Enum 13: TextureFilter (6 values)
  Name: TextureFilter
  Description: Texture parameters: filter mode
  Value[TEXTURE_FILTER_POINT]: 0
  Value[TEXTURE_FILTER_BILINEAR]: 1
  Value[TEXTURE_FILTER_TRILINEAR]: 2
  Value[TEXTURE_FILTER_ANISOTROPIC_4X]: 3
  Value[TEXTURE_FILTER_ANISOTROPIC_8X]: 4
  Value[TEXTURE_FILTER_ANISOTROPIC_16X]: 5
Enum 14: TextureWrap (4 values)
  Name: TextureWrap
  Description: Texture parameters: wrap mode
  Value[TEXTURE_WRAP_REPEAT]: 0
  Value[TEXTURE_WRAP_CLAMP]: 1
  Value[TEXTURE_WRAP_MIRROR_REPEAT]: 2
  Value[TEXTURE_WRAP_MIRROR_CLAMP]: 3
Enum 15: CubemapLayout (6 values)
  Name: CubemapLayout
  Description: Cubemap layouts
  Value[CUBEMAP_LAYOUT_AUTO_DETECT]: 0
  Value[CUBEMAP_LAYOUT_LINE_VERTICAL]: 1
  Value[CUBEMAP_LAYOUT_LINE_HORIZONTAL]: 2
  Value[CUBEMAP_LAYOUT_CROSS_THREE_BY_FOUR]: 3
  Value[CUBEMAP_LAYOUT_CROSS_FOUR_BY_THREE]: 4
  Value[CUBEMAP_LAYOUT_PANORAMA]: 5
Enum 16: FontType (3 values)
  Name: FontType
  Description: Font type, defines generation method
  Value[FONT_DEFAULT]: 0
  Value[FONT_BITMAP]: 1
  Value[FONT_SDF]: 2
Enum 17: BlendMode (8 values)
  Name: BlendMode
  Description: Color blending modes (pre-defined)
  Value[BLEND_ALPHA]: 0
  Value[BLEND_ADDITIVE]: 1
  Value[BLEND_MULTIPLIED]: 2
  Value[BLEND_ADD_COLORS]: 3
  Value[BLEND_SUBTRACT_COLORS]: 4
  Value[BLEND_ALPHA_PREMULTIPLY]: 5
  Value[BLEND_CUSTOM]: 6
  Value[BLEND_CUSTOM_SEPARATE]: 7
Enum 18: Gesture (11 values)
  Name: Gesture
  Description: Gesture
  Value[GESTURE_NONE]: 0
  Value[GESTURE_TAP]: 1
  Value[GESTURE_DOUBLETAP]: 2
  Value[GESTURE_HOLD]: 4
  Value[GESTURE_DRAG]: 8
  Value[GESTURE_SWIPE_RIGHT]: 16
  Value[GESTURE_SWIPE_LEFT]: 32
  Value[GESTURE_SWIPE_UP]: 64
  Value[GESTURE_SWIPE_DOWN]: 128
  Value[GESTURE_PINCH_IN]: 256
  Value[GESTURE_PINCH_OUT]: 512
Enum 19: CameraMode (5 values)
  Name: CameraMode
  Description: Camera system modes
  Value[CAMERA_CUSTOM]: 0
  Value[CAMERA_FREE]: 1
  Value[CAMERA_ORBITAL]: 2
  Value[CAMERA_FIRST_PERSON]: 3
  Value[CAMERA_THIRD_PERSON]: 4
Enum 20: CameraProjection (2 values)
  Name: CameraProjection
  Description: Camera projection
  Value[CAMERA_PERSPECTIVE]: 0
  Value[CAMERA_ORTHOGRAPHIC]: 1
Enum 21: NPatchLayout (3 values)
  Name: NPatchLayout
  Description: N-patch layout
  Value[NPATCH_NINE_PATCH]: 0
  Value[NPATCH_THREE_PATCH_VERTICAL]: 1
  Value[NPATCH_THREE_PATCH_HORIZONTAL]: 2

Callbacks found: 6

Callback 001: TraceLogCallback() (3 input parameters)
  Name: TraceLogCallback
  Return type: void
  Description: Logging: Redirect trace log messages
  Param[1]: logLevel (type: int)
  Param[2]: text (type: const char *)
  Param[3]: args (type: va_list)
Callback 002: LoadFileDataCallback() (2 input parameters)
  Name: LoadFileDataCallback
  Return type: unsigned char *
  Description: FileIO: Load binary data
  Param[1]: fileName (type: const char *)
  Param[2]: bytesRead (type: unsigned int *)
Callback 003: SaveFileDataCallback() (3 input parameters)
  Name: SaveFileDataCallback
  Return type: bool
  Description: FileIO: Save binary data
  Param[1]: fileName (type: const char *)
  Param[2]: data (type: void *)
  Param[3]: bytesToWrite (type: unsigned int)
Callback 004: LoadFileTextCallback() (1 input parameters)
  Name: LoadFileTextCallback
  Return type: char *
  Description: FileIO: Load text data
  Param[1]: fileName (type: const char *)
Callback 005: SaveFileTextCallback() (2 input parameters)
  Name: SaveFileTextCallback
  Return type: bool
  Description: FileIO: Save text data
  Param[1]: fileName (type: const char *)
  Param[2]: text (type: char *)
Callback 006: AudioCallback() (2 input parameters)
  Name: AudioCallback
  Return type: void
  Description: 
  Param[1]: bufferData (type: void *)
  Param[2]: frames (type: unsigned int)

Functions found: 517

Function 001: InitWindow() (3 input parameters)
  Name: InitWindow
  Return type: void
  Description: Initialize window and OpenGL context
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: title (type: const char *)
Function 002: WindowShouldClose() (0 input parameters)
  Name: WindowShouldClose
  Return type: bool
  Description: Check if KEY_ESCAPE pressed or Close icon pressed
  No input parameters
Function 003: CloseWindow() (0 input parameters)
  Name: CloseWindow
  Return type: void
  Description: Close window and unload OpenGL context
  No input parameters
Function 004: IsWindowReady() (0 input parameters)
  Name: IsWindowReady
  Return type: bool
  Description: Check if window has been initialized successfully
  No input parameters
Function 005: IsWindowFullscreen() (0 input parameters)
  Name: IsWindowFullscreen
  Return type: bool
  Description: Check if window is currently fullscreen
  No input parameters
Function 006: IsWindowHidden() (0 input parameters)
  Name: IsWindowHidden
  Return type: bool
  Description: Check if window is currently hidden (only PLATFORM_DESKTOP)
  No input parameters
Function 007: IsWindowMinimized() (0 input parameters)
  Name: IsWindowMinimized
  Return type: bool
  Description: Check if window is currently minimized (only PLATFORM_DESKTOP)
  No input parameters
Function 008: IsWindowMaximized() (0 input parameters)
  Name: IsWindowMaximized
  Return type: bool
  Description: Check if window is currently maximized (only PLATFORM_DESKTOP)
  No input parameters
Function 009: IsWindowFocused() (0 input parameters)
  Name: IsWindowFocused
  Return type: bool
  Description: Check if window is currently focused (only PLATFORM_DESKTOP)
  No input parameters
Function 010: IsWindowResized() (0 input parameters)
  Name: IsWindowResized
  Return type: bool
  Description: Check if window has been resized last frame
  No input parameters
Function 011: IsWindowState() (1 input parameters)
  Name: IsWindowState
  Return type: bool
  Description: Check if one specific window flag is enabled
  Param[1]: flag (type: unsigned int)
Function 012: SetWindowState() (1 input parameters)
  Name: SetWindowState
  Return type: void
  Description: Set window configuration state using flags (only PLATFORM_DESKTOP)
  Param[1]: flags (type: unsigned int)
Function 013: ClearWindowState() (1 input parameters)
  Name: ClearWindowState
  Return type: void
  Description: Clear window configuration state flags
  Param[1]: flags (type: unsigned int)
Function 014: ToggleFullscreen() (0 input parameters)
  Name: ToggleFullscreen
  Return type: void
  Description: Toggle window state: fullscreen/windowed (only PLATFORM_DESKTOP)
  No input parameters
Function 015: MaximizeWindow() (0 input parameters)
  Name: MaximizeWindow
  Return type: void
  Description: Set window state: maximized, if resizable (only PLATFORM_DESKTOP)
  No input parameters
Function 016: MinimizeWindow() (0 input parameters)
  Name: MinimizeWindow
  Return type: void
  Description: Set window state: minimized, if resizable (only PLATFORM_DESKTOP)
  No input parameters
Function 017: RestoreWindow() (0 input parameters)
  Name: RestoreWindow
  Return type: void
  Description: Set window state: not minimized/maximized (only PLATFORM_DESKTOP)
  No input parameters
Function 018: SetWindowIcon() (1 input parameters)
  Name: SetWindowIcon
  Return type: void
  Description: Set icon for window (single image, RGBA 32bit, only PLATFORM_DESKTOP)
  Param[1]: image (type: Image)
Function 019: SetWindowIcons() (2 input parameters)
  Name: SetWindowIcons
  Return type: void
  Description: Set icon for window (multiple images, RGBA 32bit, only PLATFORM_DESKTOP)
  Param[1]: images (type: Image *)
  Param[2]: count (type: int)
Function 020: SetWindowTitle() (1 input parameters)
  Name: SetWindowTitle
  Return type: void
  Description: Set title for window (only PLATFORM_DESKTOP)
  Param[1]: title (type: const char *)
Function 021: SetWindowPosition() (2 input parameters)
  Name: SetWindowPosition
  Return type: void
  Description: Set window position on screen (only PLATFORM_DESKTOP)
  Param[1]: x (type: int)
  Param[2]: y (type: int)
Function 022: SetWindowMonitor() (1 input parameters)
  Name: SetWindowMonitor
  Return type: void
  Description: Set monitor for the current window (fullscreen mode)
  Param[1]: monitor (type: int)
Function 023: SetWindowMinSize() (2 input parameters)
  Name: SetWindowMinSize
  Return type: void
  Description: Set window minimum dimensions (for FLAG_WINDOW_RESIZABLE)
  Param[1]: width (type: int)
  Param[2]: height (type: int)
Function 024: SetWindowSize() (2 input parameters)
  Name: SetWindowSize
  Return type: void
  Description: Set window dimensions
  Param[1]: width (type: int)
  Param[2]: height (type: int)
Function 025: SetWindowOpacity() (1 input parameters)
  Name: SetWindowOpacity
  Return type: void
  Description: Set window opacity [0.0f..1.0f] (only PLATFORM_DESKTOP)
  Param[1]: opacity (type: float)
Function 026: GetWindowHandle() (0 input parameters)
  Name: GetWindowHandle
  Return type: void *
  Description: Get native window handle
  No input parameters
Function 027: GetScreenWidth() (0 input parameters)
  Name: GetScreenWidth
  Return type: int
  Description: Get current screen width
  No input parameters
Function 028: GetScreenHeight() (0 input parameters)
  Name: GetScreenHeight
  Return type: int
  Description: Get current screen height
  No input parameters
Function 029: GetRenderWidth() (0 input parameters)
  Name: GetRenderWidth
  Return type: int
  Description: Get current render width (it considers HiDPI)
  No input parameters
Function 030: GetRenderHeight() (0 input parameters)
  Name: GetRenderHeight
  Return type: int
  Description: Get current render height (it considers HiDPI)
  No input parameters
Function 031: GetMonitorCount() (0 input parameters)
  Name: GetMonitorCount
  Return type: int
  Description: Get number of connected monitors
  No input parameters
Function 032: GetCurrentMonitor() (0 input parameters)
  Name: GetCurrentMonitor
  Return type: int
  Description: Get current connected monitor
  No input parameters
Function 033: GetMonitorPosition() (1 input parameters)
  Name: GetMonitorPosition
  Return type: Vector2
  Description: Get specified monitor position
  Param[1]: monitor (type: int)
Function 034: GetMonitorWidth() (1 input parameters)
  Name: GetMonitorWidth
  Return type: int
  Description: Get specified monitor width (current video mode used by monitor)
  Param[1]: monitor (type: int)
Function 035: GetMonitorHeight() (1 input parameters)
  Name: GetMonitorHeight
  Return type: int
  Description: Get specified monitor height (current video mode used by monitor)
  Param[1]: monitor (type: int)
Function 036: GetMonitorPhysicalWidth() (1 input parameters)
  Name: GetMonitorPhysicalWidth
  Return type: int
  Description: Get specified monitor physical width in millimetres
  Param[1]: monitor (type: int)
Function 037: GetMonitorPhysicalHeight() (1 input parameters)
  Name: GetMonitorPhysicalHeight
  Return type: int
  Description: Get specified monitor physical height in millimetres
  Param[1]: monitor (type: int)
Function 038: GetMonitorRefreshRate() (1 input parameters)
  Name: GetMonitorRefreshRate
  Return type: int
  Description: Get specified monitor refresh rate
  Param[1]: monitor (type: int)
Function 039: GetWindowPosition() (0 input parameters)
  Name: GetWindowPosition
  Return type: Vector2
  Description: Get window position XY on monitor
  No input parameters
Function 040: GetWindowScaleDPI() (0 input parameters)
  Name: GetWindowScaleDPI
  Return type: Vector2
  Description: Get window scale DPI factor
  No input parameters
Function 041: GetMonitorName() (1 input parameters)
  Name: GetMonitorName
  Return type: const char *
  Description: Get the human-readable, UTF-8 encoded name of the primary monitor
  Param[1]: monitor (type: int)
Function 042: SetClipboardText() (1 input parameters)
  Name: SetClipboardText
  Return type: void
  Description: Set clipboard text content
  Param[1]: text (type: const char *)
Function 043: GetClipboardText() (0 input parameters)
  Name: GetClipboardText
  Return type: const char *
  Description: Get clipboard text content
  No input parameters
Function 044: EnableEventWaiting() (0 input parameters)
  Name: EnableEventWaiting
  Return type: void
  Description: Enable waiting for events on EndDrawing(), no automatic event polling
  No input parameters
Function 045: DisableEventWaiting() (0 input parameters)
  Name: DisableEventWaiting
  Return type: void
  Description: Disable waiting for events on EndDrawing(), automatic events polling
  No input parameters
Function 046: SwapScreenBuffer() (0 input parameters)
  Name: SwapScreenBuffer
  Return type: void
  Description: Swap back buffer with front buffer (screen drawing)
  No input parameters
Function 047: PollInputEvents() (0 input parameters)
  Name: PollInputEvents
  Return type: void
  Description: Register all input events
  No input parameters
Function 048: WaitTime() (1 input parameters)
  Name: WaitTime
  Return type: void
  Description: Wait for some time (halt program execution)
  Param[1]: seconds (type: double)
Function 049: ShowCursor() (0 input parameters)
  Name: ShowCursor
  Return type: void
  Description: Shows cursor
  No input parameters
Function 050: HideCursor() (0 input parameters)
  Name: HideCursor
  Return type: void
  Description: Hides cursor
  No input parameters
Function 051: IsCursorHidden() (0 input parameters)
  Name: IsCursorHidden
  Return type: bool
  Description: Check if cursor is not visible
  No input parameters
Function 052: EnableCursor() (0 input parameters)
  Name: EnableCursor
  Return type: void
  Description: Enables cursor (unlock cursor)
  No input parameters
Function 053: DisableCursor() (0 input parameters)
  Name: DisableCursor
  Return type: void
  Description: Disables cursor (lock cursor)
  No input parameters
Function 054: IsCursorOnScreen() (0 input parameters)
  Name: IsCursorOnScreen
  Return type: bool
  Description: Check if cursor is on the screen
  No input parameters
Function 055: ClearBackground() (1 input parameters)
  Name: ClearBackground
  Return type: void
  Description: Set background color (framebuffer clear color)
  Param[1]: color (type: Color)
Function 056: BeginDrawing() (0 input parameters)
  Name: BeginDrawing
  Return type: void
  Description: Setup canvas (framebuffer) to start drawing
  No input parameters
Function 057: EndDrawing() (0 input parameters)
  Name: EndDrawing
  Return type: void
  Description: End canvas drawing and swap buffers (double buffering)
  No input parameters
Function 058: BeginMode2D() (1 input parameters)
  Name: BeginMode2D
  Return type: void
  Description: Begin 2D mode with custom camera (2D)
  Param[1]: camera (type: Camera2D)
Function 059: EndMode2D() (0 input parameters)
  Name: EndMode2D
  Return type: void
  Description: Ends 2D mode with custom camera
  No input parameters
Function 060: BeginMode3D() (1 input parameters)
  Name: BeginMode3D
  Return type: void
  Description: Begin 3D mode with custom camera (3D)
  Param[1]: camera (type: Camera3D)
Function 061: EndMode3D() (0 input parameters)
  Name: EndMode3D
  Return type: void
  Description: Ends 3D mode and returns to default 2D orthographic mode
  No input parameters
Function 062: BeginTextureMode() (1 input parameters)
  Name: BeginTextureMode
  Return type: void
  Description: Begin drawing to render texture
  Param[1]: target (type: RenderTexture2D)
Function 063: EndTextureMode() (0 input parameters)
  Name: EndTextureMode
  Return type: void
  Description: Ends drawing to render texture
  No input parameters
Function 064: BeginShaderMode() (1 input parameters)
  Name: BeginShaderMode
  Return type: void
  Description: Begin custom shader drawing
  Param[1]: shader (type: Shader)
Function 065: EndShaderMode() (0 input parameters)
  Name: EndShaderMode
  Return type: void
  Description: End custom shader drawing (use default shader)
  No input parameters
Function 066: BeginBlendMode() (1 input parameters)
  Name: BeginBlendMode
  Return type: void
  Description: Begin blending mode (alpha, additive, multiplied, subtract, custom)
  Param[1]: mode (type: int)
Function 067: EndBlendMode() (0 input parameters)
  Name: EndBlendMode
  Return type: void
  Description: End blending mode (reset to default: alpha blending)
  No input parameters
Function 068: BeginScissorMode() (4 input parameters)
  Name: BeginScissorMode
  Return type: void
  Description: Begin scissor mode (define screen area for following drawing)
  Param[1]: x (type: int)
  Param[2]: y (type: int)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
Function 069: EndScissorMode() (0 input parameters)
  Name: EndScissorMode
  Return type: void
  Description: End scissor mode
  No input parameters
Function 070: BeginVrStereoMode() (1 input parameters)
  Name: BeginVrStereoMode
  Return type: void
  Description: Begin stereo rendering (requires VR simulator)
  Param[1]: config (type: VrStereoConfig)
Function 071: EndVrStereoMode() (0 input parameters)
  Name: EndVrStereoMode
  Return type: void
  Description: End stereo rendering (requires VR simulator)
  No input parameters
Function 072: LoadVrStereoConfig() (1 input parameters)
  Name: LoadVrStereoConfig
  Return type: VrStereoConfig
  Description: Load VR stereo config for VR simulator device parameters
  Param[1]: device (type: VrDeviceInfo)
Function 073: UnloadVrStereoConfig() (1 input parameters)
  Name: UnloadVrStereoConfig
  Return type: void
  Description: Unload VR stereo config
  Param[1]: config (type: VrStereoConfig)
Function 074: LoadShader() (2 input parameters)
  Name: LoadShader
  Return type: Shader
  Description: Load shader from files and bind default locations
  Param[1]: vsFileName (type: const char *)
  Param[2]: fsFileName (type: const char *)
Function 075: LoadShaderFromMemory() (2 input parameters)
  Name: LoadShaderFromMemory
  Return type: Shader
  Description: Load shader from code strings and bind default locations
  Param[1]: vsCode (type: const char *)
  Param[2]: fsCode (type: const char *)
Function 076: IsShaderReady() (1 input parameters)
  Name: IsShaderReady
  Return type: bool
  Description: Check if a shader is ready
  Param[1]: shader (type: Shader)
Function 077: GetShaderLocation() (2 input parameters)
  Name: GetShaderLocation
  Return type: int
  Description: Get shader uniform location
  Param[1]: shader (type: Shader)
  Param[2]: uniformName (type: const char *)
Function 078: GetShaderLocationAttrib() (2 input parameters)
  Name: GetShaderLocationAttrib
  Return type: int
  Description: Get shader attribute location
  Param[1]: shader (type: Shader)
  Param[2]: attribName (type: const char *)
Function 079: SetShaderValue() (4 input parameters)
  Name: SetShaderValue
  Return type: void
  Description: Set shader uniform value
  Param[1]: shader (type: Shader)
  Param[2]: locIndex (type: int)
  Param[3]: value (type: const void *)
  Param[4]: uniformType (type: int)
Function 080: SetShaderValueV() (5 input parameters)
  Name: SetShaderValueV
  Return type: void
  Description: Set shader uniform value vector
  Param[1]: shader (type: Shader)
  Param[2]: locIndex (type: int)
  Param[3]: value (type: const void *)
  Param[4]: uniformType (type: int)
  Param[5]: count (type: int)
Function 081: SetShaderValueMatrix() (3 input parameters)
  Name: SetShaderValueMatrix
  Return type: void
  Description: Set shader uniform value (matrix 4x4)
  Param[1]: shader (type: Shader)
  Param[2]: locIndex (type: int)
  Param[3]: mat (type: Matrix)
Function 082: SetShaderValueTexture() (3 input parameters)
  Name: SetShaderValueTexture
  Return type: void
  Description: Set shader uniform value for texture (sampler2d)
  Param[1]: shader (type: Shader)
  Param[2]: locIndex (type: int)
  Param[3]: texture (type: Texture2D)
Function 083: UnloadShader() (1 input parameters)
  Name: UnloadShader
  Return type: void
  Description: Unload shader from GPU memory (VRAM)
  Param[1]: shader (type: Shader)
Function 084: GetMouseRay() (2 input parameters)
  Name: GetMouseRay
  Return type: Ray
  Description: Get a ray trace from mouse position
  Param[1]: mousePosition (type: Vector2)
  Param[2]: camera (type: Camera)
Function 085: GetCameraMatrix() (1 input parameters)
  Name: GetCameraMatrix
  Return type: Matrix
  Description: Get camera transform matrix (view matrix)
  Param[1]: camera (type: Camera)
Function 086: GetCameraMatrix2D() (1 input parameters)
  Name: GetCameraMatrix2D
  Return type: Matrix
  Description: Get camera 2d transform matrix
  Param[1]: camera (type: Camera2D)
Function 087: GetWorldToScreen() (2 input parameters)
  Name: GetWorldToScreen
  Return type: Vector2
  Description: Get the screen space position for a 3d world space position
  Param[1]: position (type: Vector3)
  Param[2]: camera (type: Camera)
Function 088: GetScreenToWorld2D() (2 input parameters)
  Name: GetScreenToWorld2D
  Return type: Vector2
  Description: Get the world space position for a 2d camera screen space position
  Param[1]: position (type: Vector2)
  Param[2]: camera (type: Camera2D)
Function 089: GetWorldToScreenEx() (4 input parameters)
  Name: GetWorldToScreenEx
  Return type: Vector2
  Description: Get size position for a 3d world space position
  Param[1]: position (type: Vector3)
  Param[2]: camera (type: Camera)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
Function 090: GetWorldToScreen2D() (2 input parameters)
  Name: GetWorldToScreen2D
  Return type: Vector2
  Description: Get the screen space position for a 2d camera world space position
  Param[1]: position (type: Vector2)
  Param[2]: camera (type: Camera2D)
Function 091: SetTargetFPS() (1 input parameters)
  Name: SetTargetFPS
  Return type: void
  Description: Set target FPS (maximum)
  Param[1]: fps (type: int)
Function 092: GetFPS() (0 input parameters)
  Name: GetFPS
  Return type: int
  Description: Get current FPS
  No input parameters
Function 093: GetFrameTime() (0 input parameters)
  Name: GetFrameTime
  Return type: float
  Description: Get time in seconds for last frame drawn (delta time)
  No input parameters
Function 094: GetTime() (0 input parameters)
  Name: GetTime
  Return type: double
  Description: Get elapsed time in seconds since InitWindow()
  No input parameters
Function 095: GetRandomValue() (2 input parameters)
  Name: GetRandomValue
  Return type: int
  Description: Get a random value between min and max (both included)
  Param[1]: min (type: int)
  Param[2]: max (type: int)
Function 096: SetRandomSeed() (1 input parameters)
  Name: SetRandomSeed
  Return type: void
  Description: Set the seed for the random number generator
  Param[1]: seed (type: unsigned int)
Function 097: TakeScreenshot() (1 input parameters)
  Name: TakeScreenshot
  Return type: void
  Description: Takes a screenshot of current screen (filename extension defines format)
  Param[1]: fileName (type: const char *)
Function 098: SetConfigFlags() (1 input parameters)
  Name: SetConfigFlags
  Return type: void
  Description: Setup init configuration flags (view FLAGS)
  Param[1]: flags (type: unsigned int)
Function 099: TraceLog() (3 input parameters)
  Name: TraceLog
  Return type: void
  Description: Show trace log messages (LOG_DEBUG, LOG_INFO, LOG_WARNING, LOG_ERROR...)
  Param[1]: logLevel (type: int)
  Param[2]: text (type: const char *)
  Param[3]: args (type: ...)
Function 100: SetTraceLogLevel() (1 input parameters)
  Name: SetTraceLogLevel
  Return type: void
  Description: Set the current threshold (minimum) log level
  Param[1]: logLevel (type: int)
Function 101: MemAlloc() (1 input parameters)
  Name: MemAlloc
  Return type: void *
  Description: Internal memory allocator
  Param[1]: size (type: unsigned int)
Function 102: MemRealloc() (2 input parameters)
  Name: MemRealloc
  Return type: void *
  Description: Internal memory reallocator
  Param[1]: ptr (type: void *)
  Param[2]: size (type: unsigned int)
Function 103: MemFree() (1 input parameters)
  Name: MemFree
  Return type: void
  Description: Internal memory free
  Param[1]: ptr (type: void *)
Function 104: OpenURL() (1 input parameters)
  Name: OpenURL
  Return type: void
  Description: Open URL with default system browser (if available)
  Param[1]: url (type: const char *)
Function 105: SetTraceLogCallback() (1 input parameters)
  Name: SetTraceLogCallback
  Return type: void
  Description: Set custom trace log
  Param[1]: callback (type: TraceLogCallback)
Function 106: SetLoadFileDataCallback() (1 input parameters)
  Name: SetLoadFileDataCallback
  Return type: void
  Description: Set custom file binary data loader
  Param[1]: callback (type: LoadFileDataCallback)
Function 107: SetSaveFileDataCallback() (1 input parameters)
  Name: SetSaveFileDataCallback
  Return type: void
  Description: Set custom file binary data saver
  Param[1]: callback (type: SaveFileDataCallback)
Function 108: SetLoadFileTextCallback() (1 input parameters)
  Name: SetLoadFileTextCallback
  Return type: void
  Description: Set custom file text data loader
  Param[1]: callback (type: LoadFileTextCallback)
Function 109: SetSaveFileTextCallback() (1 input parameters)
  Name: SetSaveFileTextCallback
  Return type: void
  Description: Set custom file text data saver
  Param[1]: callback (type: SaveFileTextCallback)
Function 110: LoadFileData() (2 input parameters)
  Name: LoadFileData
  Return type: unsigned char *
  Description: Load file data as byte array (read)
  Param[1]: fileName (type: const char *)
  Param[2]: bytesRead (type: unsigned int *)
Function 111: UnloadFileData() (1 input parameters)
  Name: UnloadFileData
  Return type: void
  Description: Unload file data allocated by LoadFileData()
  Param[1]: data (type: unsigned char *)
Function 112: SaveFileData() (3 input parameters)
  Name: SaveFileData
  Return type: bool
  Description: Save data to file from byte array (write), returns true on success
  Param[1]: fileName (type: const char *)
  Param[2]: data (type: void *)
  Param[3]: bytesToWrite (type: unsigned int)
Function 113: ExportDataAsCode() (3 input parameters)
  Name: ExportDataAsCode
  Return type: bool
  Description: Export data to code (.h), returns true on success
  Param[1]: data (type: const unsigned char *)
  Param[2]: size (type: unsigned int)
  Param[3]: fileName (type: const char *)
Function 114: LoadFileText() (1 input parameters)
  Name: LoadFileText
  Return type: char *
  Description: Load text data from file (read), returns a '\0' terminated string
  Param[1]: fileName (type: const char *)
Function 115: UnloadFileText() (1 input parameters)
  Name: UnloadFileText
  Return type: void
  Description: Unload file text data allocated by LoadFileText()
  Param[1]: text (type: char *)
Function 116: SaveFileText() (2 input parameters)
  Name: SaveFileText
  Return type: bool
  Description: Save text data to file (write), string must be '\0' terminated, returns true on success
  Param[1]: fileName (type: const char *)
  Param[2]: text (type: char *)
Function 117: FileExists() (1 input parameters)
  Name: FileExists
  Return type: bool
  Description: Check if file exists
  Param[1]: fileName (type: const char *)
Function 118: DirectoryExists() (1 input parameters)
  Name: DirectoryExists
  Return type: bool
  Description: Check if a directory path exists
  Param[1]: dirPath (type: const char *)
Function 119: IsFileExtension() (2 input parameters)
  Name: IsFileExtension
  Return type: bool
  Description: Check file extension (including point: .png, .wav)
  Param[1]: fileName (type: const char *)
  Param[2]: ext (type: const char *)
Function 120: GetFileLength() (1 input parameters)
  Name: GetFileLength
  Return type: int
  Description: Get file length in bytes (NOTE: GetFileSize() conflicts with windows.h)
  Param[1]: fileName (type: const char *)
Function 121: GetFileExtension() (1 input parameters)
  Name: GetFileExtension
  Return type: const char *
  Description: Get pointer to extension for a filename string (includes dot: '.png')
  Param[1]: fileName (type: const char *)
Function 122: GetFileName() (1 input parameters)
  Name: GetFileName
  Return type: const char *
  Description: Get pointer to filename for a path string
  Param[1]: filePath (type: const char *)
Function 123: GetFileNameWithoutExt() (1 input parameters)
  Name: GetFileNameWithoutExt
  Return type: const char *
  Description: Get filename string without extension (uses static string)
  Param[1]: filePath (type: const char *)
Function 124: GetDirectoryPath() (1 input parameters)
  Name: GetDirectoryPath
  Return type: const char *
  Description: Get full path for a given fileName with path (uses static string)
  Param[1]: filePath (type: const char *)
Function 125: GetPrevDirectoryPath() (1 input parameters)
  Name: GetPrevDirectoryPath
  Return type: const char *
  Description: Get previous directory path for a given path (uses static string)
  Param[1]: dirPath (type: const char *)
Function 126: GetWorkingDirectory() (0 input parameters)
  Name: GetWorkingDirectory
  Return type: const char *
  Description: Get current working directory (uses static string)
  No input parameters
Function 127: GetApplicationDirectory() (0 input parameters)
  Name: GetApplicationDirectory
  Return type: const char *
  Description: Get the directory if the running application (uses static string)
  No input parameters
Function 128: ChangeDirectory() (1 input parameters)
  Name: ChangeDirectory
  Return type: bool
  Description: Change working directory, return true on success
  Param[1]: dir (type: const char *)
Function 129: IsPathFile() (1 input parameters)
  Name: IsPathFile
  Return type: bool
  Description: Check if a given path is a file or a directory
  Param[1]: path (type: const char *)
Function 130: LoadDirectoryFiles() (1 input parameters)
  Name: LoadDirectoryFiles
  Return type: FilePathList
  Description: Load directory filepaths
  Param[1]: dirPath (type: const char *)
Function 131: LoadDirectoryFilesEx() (3 input parameters)
  Name: LoadDirectoryFilesEx
  Return type: FilePathList
  Description: Load directory filepaths with extension filtering and recursive directory scan
  Param[1]: basePath (type: const char *)
  Param[2]: filter (type: const char *)
  Param[3]: scanSubdirs (type: bool)
Function 132: UnloadDirectoryFiles() (1 input parameters)
  Name: UnloadDirectoryFiles
  Return type: void
  Description: Unload filepaths
  Param[1]: files (type: FilePathList)
Function 133: IsFileDropped() (0 input parameters)
  Name: IsFileDropped
  Return type: bool
  Description: Check if a file has been dropped into window
  No input parameters
Function 134: LoadDroppedFiles() (0 input parameters)
  Name: LoadDroppedFiles
  Return type: FilePathList
  Description: Load dropped filepaths
  No input parameters
Function 135: UnloadDroppedFiles() (1 input parameters)
  Name: UnloadDroppedFiles
  Return type: void
  Description: Unload dropped filepaths
  Param[1]: files (type: FilePathList)
Function 136: GetFileModTime() (1 input parameters)
  Name: GetFileModTime
  Return type: long
  Description: Get file modification time (last write time)
  Param[1]: fileName (type: const char *)
Function 137: CompressData() (3 input parameters)
  Name: CompressData
  Return type: unsigned char *
  Description: Compress data (DEFLATE algorithm), memory must be MemFree()
  Param[1]: data (type: const unsigned char *)
  Param[2]: dataSize (type: int)
  Param[3]: compDataSize (type: int *)
Function 138: DecompressData() (3 input parameters)
  Name: DecompressData
  Return type: unsigned char *
  Description: Decompress data (DEFLATE algorithm), memory must be MemFree()
  Param[1]: compData (type: const unsigned char *)
  Param[2]: compDataSize (type: int)
  Param[3]: dataSize (type: int *)
Function 139: EncodeDataBase64() (3 input parameters)
  Name: EncodeDataBase64
  Return type: char *
  Description: Encode data to Base64 string, memory must be MemFree()
  Param[1]: data (type: const unsigned char *)
  Param[2]: dataSize (type: int)
  Param[3]: outputSize (type: int *)
Function 140: DecodeDataBase64() (2 input parameters)
  Name: DecodeDataBase64
  Return type: unsigned char *
  Description: Decode Base64 string data, memory must be MemFree()
  Param[1]: data (type: const unsigned char *)
  Param[2]: outputSize (type: int *)
Function 141: IsKeyPressed() (1 input parameters)
  Name: IsKeyPressed
  Return type: bool
  Description: Check if a key has been pressed once
  Param[1]: key (type: int)
Function 142: IsKeyDown() (1 input parameters)
  Name: IsKeyDown
  Return type: bool
  Description: Check if a key is being pressed
  Param[1]: key (type: int)
Function 143: IsKeyReleased() (1 input parameters)
  Name: IsKeyReleased
  Return type: bool
  Description: Check if a key has been released once
  Param[1]: key (type: int)
Function 144: IsKeyUp() (1 input parameters)
  Name: IsKeyUp
  Return type: bool
  Description: Check if a key is NOT being pressed
  Param[1]: key (type: int)
Function 145: SetExitKey() (1 input parameters)
  Name: SetExitKey
  Return type: void
  Description: Set a custom key to exit program (default is ESC)
  Param[1]: key (type: int)
Function 146: GetKeyPressed() (0 input parameters)
  Name: GetKeyPressed
  Return type: int
  Description: Get key pressed (keycode), call it multiple times for keys queued, returns 0 when the queue is empty
  No input parameters
Function 147: GetCharPressed() (0 input parameters)
  Name: GetCharPressed
  Return type: int
  Description: Get char pressed (unicode), call it multiple times for chars queued, returns 0 when the queue is empty
  No input parameters
Function 148: IsGamepadAvailable() (1 input parameters)
  Name: IsGamepadAvailable
  Return type: bool
  Description: Check if a gamepad is available
  Param[1]: gamepad (type: int)
Function 149: GetGamepadName() (1 input parameters)
  Name: GetGamepadName
  Return type: const char *
  Description: Get gamepad internal name id
  Param[1]: gamepad (type: int)
Function 150: IsGamepadButtonPressed() (2 input parameters)
  Name: IsGamepadButtonPressed
  Return type: bool
  Description: Check if a gamepad button has been pressed once
  Param[1]: gamepad (type: int)
  Param[2]: button (type: int)
Function 151: IsGamepadButtonDown() (2 input parameters)
  Name: IsGamepadButtonDown
  Return type: bool
  Description: Check if a gamepad button is being pressed
  Param[1]: gamepad (type: int)
  Param[2]: button (type: int)
Function 152: IsGamepadButtonReleased() (2 input parameters)
  Name: IsGamepadButtonReleased
  Return type: bool
  Description: Check if a gamepad button has been released once
  Param[1]: gamepad (type: int)
  Param[2]: button (type: int)
Function 153: IsGamepadButtonUp() (2 input parameters)
  Name: IsGamepadButtonUp
  Return type: bool
  Description: Check if a gamepad button is NOT being pressed
  Param[1]: gamepad (type: int)
  Param[2]: button (type: int)
Function 154: GetGamepadButtonPressed() (0 input parameters)
  Name: GetGamepadButtonPressed
  Return type: int
  Description: Get the last gamepad button pressed
  No input parameters
Function 155: GetGamepadAxisCount() (1 input parameters)
  Name: GetGamepadAxisCount
  Return type: int
  Description: Get gamepad axis count for a gamepad
  Param[1]: gamepad (type: int)
Function 156: GetGamepadAxisMovement() (2 input parameters)
  Name: GetGamepadAxisMovement
  Return type: float
  Description: Get axis movement value for a gamepad axis
  Param[1]: gamepad (type: int)
  Param[2]: axis (type: int)
Function 157: SetGamepadMappings() (1 input parameters)
  Name: SetGamepadMappings
  Return type: int
  Description: Set internal gamepad mappings (SDL_GameControllerDB)
  Param[1]: mappings (type: const char *)
Function 158: IsMouseButtonPressed() (1 input parameters)
  Name: IsMouseButtonPressed
  Return type: bool
  Description: Check if a mouse button has been pressed once
  Param[1]: button (type: int)
Function 159: IsMouseButtonDown() (1 input parameters)
  Name: IsMouseButtonDown
  Return type: bool
  Description: Check if a mouse button is being pressed
  Param[1]: button (type: int)
Function 160: IsMouseButtonReleased() (1 input parameters)
  Name: IsMouseButtonReleased
  Return type: bool
  Description: Check if a mouse button has been released once
  Param[1]: button (type: int)
Function 161: IsMouseButtonUp() (1 input parameters)
  Name: IsMouseButtonUp
  Return type: bool
  Description: Check if a mouse button is NOT being pressed
  Param[1]: button (type: int)
Function 162: GetMouseX() (0 input parameters)
  Name: GetMouseX
  Return type: int
  Description: Get mouse position X
  No input parameters
Function 163: GetMouseY() (0 input parameters)
  Name: GetMouseY
  Return type: int
  Description: Get mouse position Y
  No input parameters
Function 164: GetMousePosition() (0 input parameters)
  Name: GetMousePosition
  Return type: Vector2
  Description: Get mouse position XY
  No input parameters
Function 165: GetMouseDelta() (0 input parameters)
  Name: GetMouseDelta
  Return type: Vector2
  Description: Get mouse delta between frames
  No input parameters
Function 166: SetMousePosition() (2 input parameters)
  Name: SetMousePosition
  Return type: void
  Description: Set mouse position XY
  Param[1]: x (type: int)
  Param[2]: y (type: int)
Function 167: SetMouseOffset() (2 input parameters)
  Name: SetMouseOffset
  Return type: void
  Description: Set mouse offset
  Param[1]: offsetX (type: int)
  Param[2]: offsetY (type: int)
Function 168: SetMouseScale() (2 input parameters)
  Name: SetMouseScale
  Return type: void
  Description: Set mouse scaling
  Param[1]: scaleX (type: float)
  Param[2]: scaleY (type: float)
Function 169: GetMouseWheelMove() (0 input parameters)
  Name: GetMouseWheelMove
  Return type: float
  Description: Get mouse wheel movement for X or Y, whichever is larger
  No input parameters
Function 170: GetMouseWheelMoveV() (0 input parameters)
  Name: GetMouseWheelMoveV
  Return type: Vector2
  Description: Get mouse wheel movement for both X and Y
  No input parameters
Function 171: SetMouseCursor() (1 input parameters)
  Name: SetMouseCursor
  Return type: void
  Description: Set mouse cursor
  Param[1]: cursor (type: int)
Function 172: GetTouchX() (0 input parameters)
  Name: GetTouchX
  Return type: int
  Description: Get touch position X for touch point 0 (relative to screen size)
  No input parameters
Function 173: GetTouchY() (0 input parameters)
  Name: GetTouchY
  Return type: int
  Description: Get touch position Y for touch point 0 (relative to screen size)
  No input parameters
Function 174: GetTouchPosition() (1 input parameters)
  Name: GetTouchPosition
  Return type: Vector2
  Description: Get touch position XY for a touch point index (relative to screen size)
  Param[1]: index (type: int)
Function 175: GetTouchPointId() (1 input parameters)
  Name: GetTouchPointId
  Return type: int
  Description: Get touch point identifier for given index
  Param[1]: index (type: int)
Function 176: GetTouchPointCount() (0 input parameters)
  Name: GetTouchPointCount
  Return type: int
  Description: Get number of touch points
  No input parameters
Function 177: SetGesturesEnabled() (1 input parameters)
  Name: SetGesturesEnabled
  Return type: void
  Description: Enable a set of gestures using flags
  Param[1]: flags (type: unsigned int)
Function 178: IsGestureDetected() (1 input parameters)
  Name: IsGestureDetected
  Return type: bool
  Description: Check if a gesture have been detected
  Param[1]: gesture (type: int)
Function 179: GetGestureDetected() (0 input parameters)
  Name: GetGestureDetected
  Return type: int
  Description: Get latest detected gesture
  No input parameters
Function 180: GetGestureHoldDuration() (0 input parameters)
  Name: GetGestureHoldDuration
  Return type: float
  Description: Get gesture hold time in milliseconds
  No input parameters
Function 181: GetGestureDragVector() (0 input parameters)
  Name: GetGestureDragVector
  Return type: Vector2
  Description: Get gesture drag vector
  No input parameters
Function 182: GetGestureDragAngle() (0 input parameters)
  Name: GetGestureDragAngle
  Return type: float
  Description: Get gesture drag angle
  No input parameters
Function 183: GetGesturePinchVector() (0 input parameters)
  Name: GetGesturePinchVector
  Return type: Vector2
  Description: Get gesture pinch delta
  No input parameters
Function 184: GetGesturePinchAngle() (0 input parameters)
  Name: GetGesturePinchAngle
  Return type: float
  Description: Get gesture pinch angle
  No input parameters
Function 185: UpdateCamera() (2 input parameters)
  Name: UpdateCamera
  Return type: void
  Description: Update camera position for selected mode
  Param[1]: camera (type: Camera *)
  Param[2]: mode (type: int)
Function 186: UpdateCameraPro() (4 input parameters)
  Name: UpdateCameraPro
  Return type: void
  Description: Update camera movement/rotation
  Param[1]: camera (type: Camera *)
  Param[2]: movement (type: Vector3)
  Param[3]: rotation (type: Vector3)
  Param[4]: zoom (type: float)
Function 187: SetShapesTexture() (2 input parameters)
  Name: SetShapesTexture
  Return type: void
  Description: Set texture and rectangle to be used on shapes drawing
  Param[1]: texture (type: Texture2D)
  Param[2]: source (type: Rectangle)
Function 188: DrawPixel() (3 input parameters)
  Name: DrawPixel
  Return type: void
  Description: Draw a pixel
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
  Param[3]: color (type: Color)
Function 189: DrawPixelV() (2 input parameters)
  Name: DrawPixelV
  Return type: void
  Description: Draw a pixel (Vector version)
  Param[1]: position (type: Vector2)
  Param[2]: color (type: Color)
Function 190: DrawLine() (5 input parameters)
  Name: DrawLine
  Return type: void
  Description: Draw a line
  Param[1]: startPosX (type: int)
  Param[2]: startPosY (type: int)
  Param[3]: endPosX (type: int)
  Param[4]: endPosY (type: int)
  Param[5]: color (type: Color)
Function 191: DrawLineV() (3 input parameters)
  Name: DrawLineV
  Return type: void
  Description: Draw a line (Vector version)
  Param[1]: startPos (type: Vector2)
  Param[2]: endPos (type: Vector2)
  Param[3]: color (type: Color)
Function 192: DrawLineEx() (4 input parameters)
  Name: DrawLineEx
  Return type: void
  Description: Draw a line defining thickness
  Param[1]: startPos (type: Vector2)
  Param[2]: endPos (type: Vector2)
  Param[3]: thick (type: float)
  Param[4]: color (type: Color)
Function 193: DrawLineBezier() (4 input parameters)
  Name: DrawLineBezier
  Return type: void
  Description: Draw a line using cubic-bezier curves in-out
  Param[1]: startPos (type: Vector2)
  Param[2]: endPos (type: Vector2)
  Param[3]: thick (type: float)
  Param[4]: color (type: Color)
Function 194: DrawLineBezierQuad() (5 input parameters)
  Name: DrawLineBezierQuad
  Return type: void
  Description: Draw line using quadratic bezier curves with a control point
  Param[1]: startPos (type: Vector2)
  Param[2]: endPos (type: Vector2)
  Param[3]: controlPos (type: Vector2)
  Param[4]: thick (type: float)
  Param[5]: color (type: Color)
Function 195: DrawLineBezierCubic() (6 input parameters)
  Name: DrawLineBezierCubic
  Return type: void
  Description: Draw line using cubic bezier curves with 2 control points
  Param[1]: startPos (type: Vector2)
  Param[2]: endPos (type: Vector2)
  Param[3]: startControlPos (type: Vector2)
  Param[4]: endControlPos (type: Vector2)
  Param[5]: thick (type: float)
  Param[6]: color (type: Color)
Function 196: DrawLineStrip() (3 input parameters)
  Name: DrawLineStrip
  Return type: void
  Description: Draw lines sequence
  Param[1]: points (type: Vector2 *)
  Param[2]: pointCount (type: int)
  Param[3]: color (type: Color)
Function 197: DrawCircle() (4 input parameters)
  Name: DrawCircle
  Return type: void
  Description: Draw a color-filled circle
  Param[1]: centerX (type: int)
  Param[2]: centerY (type: int)
  Param[3]: radius (type: float)
  Param[4]: color (type: Color)
Function 198: DrawCircleSector() (6 input parameters)
  Name: DrawCircleSector
  Return type: void
  Description: Draw a piece of a circle
  Param[1]: center (type: Vector2)
  Param[2]: radius (type: float)
  Param[3]: startAngle (type: float)
  Param[4]: endAngle (type: float)
  Param[5]: segments (type: int)
  Param[6]: color (type: Color)
Function 199: DrawCircleSectorLines() (6 input parameters)
  Name: DrawCircleSectorLines
  Return type: void
  Description: Draw circle sector outline
  Param[1]: center (type: Vector2)
  Param[2]: radius (type: float)
  Param[3]: startAngle (type: float)
  Param[4]: endAngle (type: float)
  Param[5]: segments (type: int)
  Param[6]: color (type: Color)
Function 200: DrawCircleGradient() (5 input parameters)
  Name: DrawCircleGradient
  Return type: void
  Description: Draw a gradient-filled circle
  Param[1]: centerX (type: int)
  Param[2]: centerY (type: int)
  Param[3]: radius (type: float)
  Param[4]: color1 (type: Color)
  Param[5]: color2 (type: Color)
Function 201: DrawCircleV() (3 input parameters)
  Name: DrawCircleV
  Return type: void
  Description: Draw a color-filled circle (Vector version)
  Param[1]: center (type: Vector2)
  Param[2]: radius (type: float)
  Param[3]: color (type: Color)
Function 202: DrawCircleLines() (4 input parameters)
  Name: DrawCircleLines
  Return type: void
  Description: Draw circle outline
  Param[1]: centerX (type: int)
  Param[2]: centerY (type: int)
  Param[3]: radius (type: float)
  Param[4]: color (type: Color)
Function 203: DrawEllipse() (5 input parameters)
  Name: DrawEllipse
  Return type: void
  Description: Draw ellipse
  Param[1]: centerX (type: int)
  Param[2]: centerY (type: int)
  Param[3]: radiusH (type: float)
  Param[4]: radiusV (type: float)
  Param[5]: color (type: Color)
Function 204: DrawEllipseLines() (5 input parameters)
  Name: DrawEllipseLines
  Return type: void
  Description: Draw ellipse outline
  Param[1]: centerX (type: int)
  Param[2]: centerY (type: int)
  Param[3]: radiusH (type: float)
  Param[4]: radiusV (type: float)
  Param[5]: color (type: Color)
Function 205: DrawRing() (7 input parameters)
  Name: DrawRing
  Return type: void
  Description: Draw ring
  Param[1]: center (type: Vector2)
  Param[2]: innerRadius (type: float)
  Param[3]: outerRadius (type: float)
  Param[4]: startAngle (type: float)
  Param[5]: endAngle (type: float)
  Param[6]: segments (type: int)
  Param[7]: color (type: Color)
Function 206: DrawRingLines() (7 input parameters)
  Name: DrawRingLines
  Return type: void
  Description: Draw ring outline
  Param[1]: center (type: Vector2)
  Param[2]: innerRadius (type: float)
  Param[3]: outerRadius (type: float)
  Param[4]: startAngle (type: float)
  Param[5]: endAngle (type: float)
  Param[6]: segments (type: int)
  Param[7]: color (type: Color)
Function 207: DrawRectangle() (5 input parameters)
  Name: DrawRectangle
  Return type: void
  Description: Draw a color-filled rectangle
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
  Param[5]: color (type: Color)
Function 208: DrawRectangleV() (3 input parameters)
  Name: DrawRectangleV
  Return type: void
  Description: Draw a color-filled rectangle (Vector version)
  Param[1]: position (type: Vector2)
  Param[2]: size (type: Vector2)
  Param[3]: color (type: Color)
Function 209: DrawRectangleRec() (2 input parameters)
  Name: DrawRectangleRec
  Return type: void
  Description: Draw a color-filled rectangle
  Param[1]: rec (type: Rectangle)
  Param[2]: color (type: Color)
Function 210: DrawRectanglePro() (4 input parameters)
  Name: DrawRectanglePro
  Return type: void
  Description: Draw a color-filled rectangle with pro parameters
  Param[1]: rec (type: Rectangle)
  Param[2]: origin (type: Vector2)
  Param[3]: rotation (type: float)
  Param[4]: color (type: Color)
Function 211: DrawRectangleGradientV() (6 input parameters)
  Name: DrawRectangleGradientV
  Return type: void
  Description: Draw a vertical-gradient-filled rectangle
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
  Param[5]: color1 (type: Color)
  Param[6]: color2 (type: Color)
Function 212: DrawRectangleGradientH() (6 input parameters)
  Name: DrawRectangleGradientH
  Return type: void
  Description: Draw a horizontal-gradient-filled rectangle
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
  Param[5]: color1 (type: Color)
  Param[6]: color2 (type: Color)
Function 213: DrawRectangleGradientEx() (5 input parameters)
  Name: DrawRectangleGradientEx
  Return type: void
  Description: Draw a gradient-filled rectangle with custom vertex colors
  Param[1]: rec (type: Rectangle)
  Param[2]: col1 (type: Color)
  Param[3]: col2 (type: Color)
  Param[4]: col3 (type: Color)
  Param[5]: col4 (type: Color)
Function 214: DrawRectangleLines() (5 input parameters)
  Name: DrawRectangleLines
  Return type: void
  Description: Draw rectangle outline
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
  Param[3]: width (type: int)
  Param[4]: height (type: int)
  Param[5]: color (type: Color)
Function 215: DrawRectangleLinesEx() (3 input parameters)
  Name: DrawRectangleLinesEx
  Return type: void
  Description: Draw rectangle outline with extended parameters
  Param[1]: rec (type: Rectangle)
  Param[2]: lineThick (type: float)
  Param[3]: color (type: Color)
Function 216: DrawRectangleRounded() (4 input parameters)
  Name: DrawRectangleRounded
  Return type: void
  Description: Draw rectangle with rounded edges
  Param[1]: rec (type: Rectangle)
  Param[2]: roundness (type: float)
  Param[3]: segments (type: int)
  Param[4]: color (type: Color)
Function 217: DrawRectangleRoundedLines() (5 input parameters)
  Name: DrawRectangleRoundedLines
  Return type: void
  Description: Draw rectangle with rounded edges outline
  Param[1]: rec (type: Rectangle)
  Param[2]: roundness (type: float)
  Param[3]: segments (type: int)
  Param[4]: lineThick (type: float)
  Param[5]: color (type: Color)
Function 218: DrawTriangle() (4 input parameters)
  Name: DrawTriangle
  Return type: void
  Description: Draw a color-filled triangle (vertex in counter-clockwise order!)
  Param[1]: v1 (type: Vector2)
  Param[2]: v2 (type: Vector2)
  Param[3]: v3 (type: Vector2)
  Param[4]: color (type: Color)
Function 219: DrawTriangleLines() (4 input parameters)
  Name: DrawTriangleLines
  Return type: void
  Description: Draw triangle outline (vertex in counter-clockwise order!)
  Param[1]: v1 (type: Vector2)
  Param[2]: v2 (type: Vector2)
  Param[3]: v3 (type: Vector2)
  Param[4]: color (type: Color)
Function 220: DrawTriangleFan() (3 input parameters)
  Name: DrawTriangleFan
  Return type: void
  Description: Draw a triangle fan defined by points (first vertex is the center)
  Param[1]: points (type: Vector2 *)
  Param[2]: pointCount (type: int)
  Param[3]: color (type: Color)
Function 221: DrawTriangleStrip() (3 input parameters)
  Name: DrawTriangleStrip
  Return type: void
  Description: Draw a triangle strip defined by points
  Param[1]: points (type: Vector2 *)
  Param[2]: pointCount (type: int)
  Param[3]: color (type: Color)
Function 222: DrawPoly() (5 input parameters)
  Name: DrawPoly
  Return type: void
  Description: Draw a regular polygon (Vector version)
  Param[1]: center (type: Vector2)
  Param[2]: sides (type: int)
  Param[3]: radius (type: float)
  Param[4]: rotation (type: float)
  Param[5]: color (type: Color)
Function 223: DrawPolyLines() (5 input parameters)
  Name: DrawPolyLines
  Return type: void
  Description: Draw a polygon outline of n sides
  Param[1]: center (type: Vector2)
  Param[2]: sides (type: int)
  Param[3]: radius (type: float)
  Param[4]: rotation (type: float)
  Param[5]: color (type: Color)
Function 224: DrawPolyLinesEx() (6 input parameters)
  Name: DrawPolyLinesEx
  Return type: void
  Description: Draw a polygon outline of n sides with extended parameters
  Param[1]: center (type: Vector2)
  Param[2]: sides (type: int)
  Param[3]: radius (type: float)
  Param[4]: rotation (type: float)
  Param[5]: lineThick (type: float)
  Param[6]: color (type: Color)
Function 225: CheckCollisionRecs() (2 input parameters)
  Name: CheckCollisionRecs
  Return type: bool
  Description: Check collision between two rectangles
  Param[1]: rec1 (type: Rectangle)
  Param[2]: rec2 (type: Rectangle)
Function 226: CheckCollisionCircles() (4 input parameters)
  Name: CheckCollisionCircles
  Return type: bool
  Description: Check collision between two circles
  Param[1]: center1 (type: Vector2)
  Param[2]: radius1 (type: float)
  Param[3]: center2 (type: Vector2)
  Param[4]: radius2 (type: float)
Function 227: CheckCollisionCircleRec() (3 input parameters)
  Name: CheckCollisionCircleRec
  Return type: bool
  Description: Check collision between circle and rectangle
  Param[1]: center (type: Vector2)
  Param[2]: radius (type: float)
  Param[3]: rec (type: Rectangle)
Function 228: CheckCollisionPointRec() (2 input parameters)
  Name: CheckCollisionPointRec
  Return type: bool
  Description: Check if point is inside rectangle
  Param[1]: point (type: Vector2)
  Param[2]: rec (type: Rectangle)
Function 229: CheckCollisionPointCircle() (3 input parameters)
  Name: CheckCollisionPointCircle
  Return type: bool
  Description: Check if point is inside circle
  Param[1]: point (type: Vector2)
  Param[2]: center (type: Vector2)
  Param[3]: radius (type: float)
Function 230: CheckCollisionPointTriangle() (4 input parameters)
  Name: CheckCollisionPointTriangle
  Return type: bool
  Description: Check if point is inside a triangle
  Param[1]: point (type: Vector2)
  Param[2]: p1 (type: Vector2)
  Param[3]: p2 (type: Vector2)
  Param[4]: p3 (type: Vector2)
Function 231: CheckCollisionPointPoly() (3 input parameters)
  Name: CheckCollisionPointPoly
  Return type: bool
  Description: Check if point is within a polygon described by array of vertices
  Param[1]: point (type: Vector2)
  Param[2]: points (type: Vector2 *)
  Param[3]: pointCount (type: int)
Function 232: CheckCollisionLines() (5 input parameters)
  Name: CheckCollisionLines
  Return type: bool
  Description: Check the collision between two lines defined by two points each, returns collision point by reference
  Param[1]: startPos1 (type: Vector2)
  Param[2]: endPos1 (type: Vector2)
  Param[3]: startPos2 (type: Vector2)
  Param[4]: endPos2 (type: Vector2)
  Param[5]: collisionPoint (type: Vector2 *)
Function 233: CheckCollisionPointLine() (4 input parameters)
  Name: CheckCollisionPointLine
  Return type: bool
  Description: Check if point belongs to line created between two points [p1] and [p2] with defined margin in pixels [threshold]
  Param[1]: point (type: Vector2)
  Param[2]: p1 (type: Vector2)
  Param[3]: p2 (type: Vector2)
  Param[4]: threshold (type: int)
Function 234: GetCollisionRec() (2 input parameters)
  Name: GetCollisionRec
  Return type: Rectangle
  Description: Get collision rectangle for two rectangles collision
  Param[1]: rec1 (type: Rectangle)
  Param[2]: rec2 (type: Rectangle)
Function 235: LoadImage() (1 input parameters)
  Name: LoadImage
  Return type: Image
  Description: Load image from file into CPU memory (RAM)
  Param[1]: fileName (type: const char *)
Function 236: LoadImageRaw() (5 input parameters)
  Name: LoadImageRaw
  Return type: Image
  Description: Load image from RAW file data
  Param[1]: fileName (type: const char *)
  Param[2]: width (type: int)
  Param[3]: height (type: int)
  Param[4]: format (type: int)
  Param[5]: headerSize (type: int)
Function 237: LoadImageAnim() (2 input parameters)
  Name: LoadImageAnim
  Return type: Image
  Description: Load image sequence from file (frames appended to image.data)
  Param[1]: fileName (type: const char *)
  Param[2]: frames (type: int *)
Function 238: LoadImageFromMemory() (3 input parameters)
  Name: LoadImageFromMemory
  Return type: Image
  Description: Load image from memory buffer, fileType refers to extension: i.e. '.png'
  Param[1]: fileType (type: const char *)
  Param[2]: fileData (type: const unsigned char *)
  Param[3]: dataSize (type: int)
Function 239: LoadImageFromTexture() (1 input parameters)
  Name: LoadImageFromTexture
  Return type: Image
  Description: Load image from GPU texture data
  Param[1]: texture (type: Texture2D)
Function 240: LoadImageFromScreen() (0 input parameters)
  Name: LoadImageFromScreen
  Return type: Image
  Description: Load image from screen buffer and (screenshot)
  No input parameters
Function 241: IsImageReady() (1 input parameters)
  Name: IsImageReady
  Return type: bool
  Description: Check if an image is ready
  Param[1]: image (type: Image)
Function 242: UnloadImage() (1 input parameters)
  Name: UnloadImage
  Return type: void
  Description: Unload image from CPU memory (RAM)
  Param[1]: image (type: Image)
Function 243: ExportImage() (2 input parameters)
  Name: ExportImage
  Return type: bool
  Description: Export image data to file, returns true on success
  Param[1]: image (type: Image)
  Param[2]: fileName (type: const char *)
Function 244: ExportImageAsCode() (2 input parameters)
  Name: ExportImageAsCode
  Return type: bool
  Description: Export image as code file defining an array of bytes, returns true on success
  Param[1]: image (type: Image)
  Param[2]: fileName (type: const char *)
Function 245: GenImageColor() (3 input parameters)
  Name: GenImageColor
  Return type: Image
  Description: Generate image: plain color
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: color (type: Color)
Function 246: GenImageGradientV() (4 input parameters)
  Name: GenImageGradientV
  Return type: Image
  Description: Generate image: vertical gradient
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: top (type: Color)
  Param[4]: bottom (type: Color)
Function 247: GenImageGradientH() (4 input parameters)
  Name: GenImageGradientH
  Return type: Image
  Description: Generate image: horizontal gradient
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: left (type: Color)
  Param[4]: right (type: Color)
Function 248: GenImageGradientRadial() (5 input parameters)
  Name: GenImageGradientRadial
  Return type: Image
  Description: Generate image: radial gradient
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: density (type: float)
  Param[4]: inner (type: Color)
  Param[5]: outer (type: Color)
Function 249: GenImageChecked() (6 input parameters)
  Name: GenImageChecked
  Return type: Image
  Description: Generate image: checked
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: checksX (type: int)
  Param[4]: checksY (type: int)
  Param[5]: col1 (type: Color)
  Param[6]: col2 (type: Color)
Function 250: GenImageWhiteNoise() (3 input parameters)
  Name: GenImageWhiteNoise
  Return type: Image
  Description: Generate image: white noise
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: factor (type: float)
Function 251: GenImagePerlinNoise() (5 input parameters)
  Name: GenImagePerlinNoise
  Return type: Image
  Description: Generate image: perlin noise
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: offsetX (type: int)
  Param[4]: offsetY (type: int)
  Param[5]: scale (type: float)
Function 252: GenImageCellular() (3 input parameters)
  Name: GenImageCellular
  Return type: Image
  Description: Generate image: cellular algorithm, bigger tileSize means bigger cells
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: tileSize (type: int)
Function 253: GenImageText() (3 input parameters)
  Name: GenImageText
  Return type: Image
  Description: Generate image: grayscale image from text data
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: text (type: const char *)
Function 254: ImageCopy() (1 input parameters)
  Name: ImageCopy
  Return type: Image
  Description: Create an image duplicate (useful for transformations)
  Param[1]: image (type: Image)
Function 255: ImageFromImage() (2 input parameters)
  Name: ImageFromImage
  Return type: Image
  Description: Create an image from another image piece
  Param[1]: image (type: Image)
  Param[2]: rec (type: Rectangle)
Function 256: ImageText() (3 input parameters)
  Name: ImageText
  Return type: Image
  Description: Create an image from text (default font)
  Param[1]: text (type: const char *)
  Param[2]: fontSize (type: int)
  Param[3]: color (type: Color)
Function 257: ImageTextEx() (5 input parameters)
  Name: ImageTextEx
  Return type: Image
  Description: Create an image from text (custom sprite font)
  Param[1]: font (type: Font)
  Param[2]: text (type: const char *)
  Param[3]: fontSize (type: float)
  Param[4]: spacing (type: float)
  Param[5]: tint (type: Color)
Function 258: ImageFormat() (2 input parameters)
  Name: ImageFormat
  Return type: void
  Description: Convert image data to desired format
  Param[1]: image (type: Image *)
  Param[2]: newFormat (type: int)
Function 259: ImageToPOT() (2 input parameters)
  Name: ImageToPOT
  Return type: void
  Description: Convert image to POT (power-of-two)
  Param[1]: image (type: Image *)
  Param[2]: fill (type: Color)
Function 260: ImageCrop() (2 input parameters)
  Name: ImageCrop
  Return type: void
  Description: Crop an image to a defined rectangle
  Param[1]: image (type: Image *)
  Param[2]: crop (type: Rectangle)
Function 261: ImageAlphaCrop() (2 input parameters)
  Name: ImageAlphaCrop
  Return type: void
  Description: Crop image depending on alpha value
  Param[1]: image (type: Image *)
  Param[2]: threshold (type: float)
Function 262: ImageAlphaClear() (3 input parameters)
  Name: ImageAlphaClear
  Return type: void
  Description: Clear alpha channel to desired color
  Param[1]: image (type: Image *)
  Param[2]: color (type: Color)
  Param[3]: threshold (type: float)
Function 263: ImageAlphaMask() (2 input parameters)
  Name: ImageAlphaMask
  Return type: void
  Description: Apply alpha mask to image
  Param[1]: image (type: Image *)
  Param[2]: alphaMask (type: Image)
Function 264: ImageAlphaPremultiply() (1 input parameters)
  Name: ImageAlphaPremultiply
  Return type: void
  Description: Premultiply alpha channel
  Param[1]: image (type: Image *)
Function 265: ImageBlurGaussian() (2 input parameters)
  Name: ImageBlurGaussian
  Return type: void
  Description: Apply Gaussian blur using a box blur approximation
  Param[1]: image (type: Image *)
  Param[2]: blurSize (type: int)
Function 266: ImageResize() (3 input parameters)
  Name: ImageResize
  Return type: void
  Description: Resize image (Bicubic scaling algorithm)
  Param[1]: image (type: Image *)
  Param[2]: newWidth (type: int)
  Param[3]: newHeight (type: int)
Function 267: ImageResizeNN() (3 input parameters)
  Name: ImageResizeNN
  Return type: void
  Description: Resize image (Nearest-Neighbor scaling algorithm)
  Param[1]: image (type: Image *)
  Param[2]: newWidth (type: int)
  Param[3]: newHeight (type: int)
Function 268: ImageResizeCanvas() (6 input parameters)
  Name: ImageResizeCanvas
  Return type: void
  Description: Resize canvas and fill with color
  Param[1]: image (type: Image *)
  Param[2]: newWidth (type: int)
  Param[3]: newHeight (type: int)
  Param[4]: offsetX (type: int)
  Param[5]: offsetY (type: int)
  Param[6]: fill (type: Color)
Function 269: ImageMipmaps() (1 input parameters)
  Name: ImageMipmaps
  Return type: void
  Description: Compute all mipmap levels for a provided image
  Param[1]: image (type: Image *)
Function 270: ImageDither() (5 input parameters)
  Name: ImageDither
  Return type: void
  Description: Dither image data to 16bpp or lower (Floyd-Steinberg dithering)
  Param[1]: image (type: Image *)
  Param[2]: rBpp (type: int)
  Param[3]: gBpp (type: int)
  Param[4]: bBpp (type: int)
  Param[5]: aBpp (type: int)
Function 271: ImageFlipVertical() (1 input parameters)
  Name: ImageFlipVertical
  Return type: void
  Description: Flip image vertically
  Param[1]: image (type: Image *)
Function 272: ImageFlipHorizontal() (1 input parameters)
  Name: ImageFlipHorizontal
  Return type: void
  Description: Flip image horizontally
  Param[1]: image (type: Image *)
Function 273: ImageRotateCW() (1 input parameters)
  Name: ImageRotateCW
  Return type: void
  Description: Rotate image clockwise 90deg
  Param[1]: image (type: Image *)
Function 274: ImageRotateCCW() (1 input parameters)
  Name: ImageRotateCCW
  Return type: void
  Description: Rotate image counter-clockwise 90deg
  Param[1]: image (type: Image *)
Function 275: ImageColorTint() (2 input parameters)
  Name: ImageColorTint
  Return type: void
  Description: Modify image color: tint
  Param[1]: image (type: Image *)
  Param[2]: color (type: Color)
Function 276: ImageColorInvert() (1 input parameters)
  Name: ImageColorInvert
  Return type: void
  Description: Modify image color: invert
  Param[1]: image (type: Image *)
Function 277: ImageColorGrayscale() (1 input parameters)
  Name: ImageColorGrayscale
  Return type: void
  Description: Modify image color: grayscale
  Param[1]: image (type: Image *)
Function 278: ImageColorContrast() (2 input parameters)
  Name: ImageColorContrast
  Return type: void
  Description: Modify image color: contrast (-100 to 100)
  Param[1]: image (type: Image *)
  Param[2]: contrast (type: float)
Function 279: ImageColorBrightness() (2 input parameters)
  Name: ImageColorBrightness
  Return type: void
  Description: Modify image color: brightness (-255 to 255)
  Param[1]: image (type: Image *)
  Param[2]: brightness (type: int)
Function 280: ImageColorReplace() (3 input parameters)
  Name: ImageColorReplace
  Return type: void
  Description: Modify image color: replace color
  Param[1]: image (type: Image *)
  Param[2]: color (type: Color)
  Param[3]: replace (type: Color)
Function 281: LoadImageColors() (1 input parameters)
  Name: LoadImageColors
  Return type: Color *
  Description: Load color data from image as a Color array (RGBA - 32bit)
  Param[1]: image (type: Image)
Function 282: LoadImagePalette() (3 input parameters)
  Name: LoadImagePalette
  Return type: Color *
  Description: Load colors palette from image as a Color array (RGBA - 32bit)
  Param[1]: image (type: Image)
  Param[2]: maxPaletteSize (type: int)
  Param[3]: colorCount (type: int *)
Function 283: UnloadImageColors() (1 input parameters)
  Name: UnloadImageColors
  Return type: void
  Description: Unload color data loaded with LoadImageColors()
  Param[1]: colors (type: Color *)
Function 284: UnloadImagePalette() (1 input parameters)
  Name: UnloadImagePalette
  Return type: void
  Description: Unload colors palette loaded with LoadImagePalette()
  Param[1]: colors (type: Color *)
Function 285: GetImageAlphaBorder() (2 input parameters)
  Name: GetImageAlphaBorder
  Return type: Rectangle
  Description: Get image alpha border rectangle
  Param[1]: image (type: Image)
  Param[2]: threshold (type: float)
Function 286: GetImageColor() (3 input parameters)
  Name: GetImageColor
  Return type: Color
  Description: Get image pixel color at (x, y) position
  Param[1]: image (type: Image)
  Param[2]: x (type: int)
  Param[3]: y (type: int)
Function 287: ImageClearBackground() (2 input parameters)
  Name: ImageClearBackground
  Return type: void
  Description: Clear image background with given color
  Param[1]: dst (type: Image *)
  Param[2]: color (type: Color)
Function 288: ImageDrawPixel() (4 input parameters)
  Name: ImageDrawPixel
  Return type: void
  Description: Draw pixel within an image
  Param[1]: dst (type: Image *)
  Param[2]: posX (type: int)
  Param[3]: posY (type: int)
  Param[4]: color (type: Color)
Function 289: ImageDrawPixelV() (3 input parameters)
  Name: ImageDrawPixelV
  Return type: void
  Description: Draw pixel within an image (Vector version)
  Param[1]: dst (type: Image *)
  Param[2]: position (type: Vector2)
  Param[3]: color (type: Color)
Function 290: ImageDrawLine() (6 input parameters)
  Name: ImageDrawLine
  Return type: void
  Description: Draw line within an image
  Param[1]: dst (type: Image *)
  Param[2]: startPosX (type: int)
  Param[3]: startPosY (type: int)
  Param[4]: endPosX (type: int)
  Param[5]: endPosY (type: int)
  Param[6]: color (type: Color)
Function 291: ImageDrawLineV() (4 input parameters)
  Name: ImageDrawLineV
  Return type: void
  Description: Draw line within an image (Vector version)
  Param[1]: dst (type: Image *)
  Param[2]: start (type: Vector2)
  Param[3]: end (type: Vector2)
  Param[4]: color (type: Color)
Function 292: ImageDrawCircle() (5 input parameters)
  Name: ImageDrawCircle
  Return type: void
  Description: Draw a filled circle within an image
  Param[1]: dst (type: Image *)
  Param[2]: centerX (type: int)
  Param[3]: centerY (type: int)
  Param[4]: radius (type: int)
  Param[5]: color (type: Color)
Function 293: ImageDrawCircleV() (4 input parameters)
  Name: ImageDrawCircleV
  Return type: void
  Description: Draw a filled circle within an image (Vector version)
  Param[1]: dst (type: Image *)
  Param[2]: center (type: Vector2)
  Param[3]: radius (type: int)
  Param[4]: color (type: Color)
Function 294: ImageDrawCircleLines() (5 input parameters)
  Name: ImageDrawCircleLines
  Return type: void
  Description: Draw circle outline within an image
  Param[1]: dst (type: Image *)
  Param[2]: centerX (type: int)
  Param[3]: centerY (type: int)
  Param[4]: radius (type: int)
  Param[5]: color (type: Color)
Function 295: ImageDrawCircleLinesV() (4 input parameters)
  Name: ImageDrawCircleLinesV
  Return type: void
  Description: Draw circle outline within an image (Vector version)
  Param[1]: dst (type: Image *)
  Param[2]: center (type: Vector2)
  Param[3]: radius (type: int)
  Param[4]: color (type: Color)
Function 296: ImageDrawRectangle() (6 input parameters)
  Name: ImageDrawRectangle
  Return type: void
  Description: Draw rectangle within an image
  Param[1]: dst (type: Image *)
  Param[2]: posX (type: int)
  Param[3]: posY (type: int)
  Param[4]: width (type: int)
  Param[5]: height (type: int)
  Param[6]: color (type: Color)
Function 297: ImageDrawRectangleV() (4 input parameters)
  Name: ImageDrawRectangleV
  Return type: void
  Description: Draw rectangle within an image (Vector version)
  Param[1]: dst (type: Image *)
  Param[2]: position (type: Vector2)
  Param[3]: size (type: Vector2)
  Param[4]: color (type: Color)
Function 298: ImageDrawRectangleRec() (3 input parameters)
  Name: ImageDrawRectangleRec
  Return type: void
  Description: Draw rectangle within an image
  Param[1]: dst (type: Image *)
  Param[2]: rec (type: Rectangle)
  Param[3]: color (type: Color)
Function 299: ImageDrawRectangleLines() (4 input parameters)
  Name: ImageDrawRectangleLines
  Return type: void
  Description: Draw rectangle lines within an image
  Param[1]: dst (type: Image *)
  Param[2]: rec (type: Rectangle)
  Param[3]: thick (type: int)
  Param[4]: color (type: Color)
Function 300: ImageDraw() (5 input parameters)
  Name: ImageDraw
  Return type: void
  Description: Draw a source image within a destination image (tint applied to source)
  Param[1]: dst (type: Image *)
  Param[2]: src (type: Image)
  Param[3]: srcRec (type: Rectangle)
  Param[4]: dstRec (type: Rectangle)
  Param[5]: tint (type: Color)
Function 301: ImageDrawText() (6 input parameters)
  Name: ImageDrawText
  Return type: void
  Description: Draw text (using default font) within an image (destination)
  Param[1]: dst (type: Image *)
  Param[2]: text (type: const char *)
  Param[3]: posX (type: int)
  Param[4]: posY (type: int)
  Param[5]: fontSize (type: int)
  Param[6]: color (type: Color)
Function 302: ImageDrawTextEx() (7 input parameters)
  Name: ImageDrawTextEx
  Return type: void
  Description: Draw text (custom sprite font) within an image (destination)
  Param[1]: dst (type: Image *)
  Param[2]: font (type: Font)
  Param[3]: text (type: const char *)
  Param[4]: position (type: Vector2)
  Param[5]: fontSize (type: float)
  Param[6]: spacing (type: float)
  Param[7]: tint (type: Color)
Function 303: LoadTexture() (1 input parameters)
  Name: LoadTexture
  Return type: Texture2D
  Description: Load texture from file into GPU memory (VRAM)
  Param[1]: fileName (type: const char *)
Function 304: LoadTextureFromImage() (1 input parameters)
  Name: LoadTextureFromImage
  Return type: Texture2D
  Description: Load texture from image data
  Param[1]: image (type: Image)
Function 305: LoadTextureCubemap() (2 input parameters)
  Name: LoadTextureCubemap
  Return type: TextureCubemap
  Description: Load cubemap from image, multiple image cubemap layouts supported
  Param[1]: image (type: Image)
  Param[2]: layout (type: int)
Function 306: LoadRenderTexture() (2 input parameters)
  Name: LoadRenderTexture
  Return type: RenderTexture2D
  Description: Load texture for rendering (framebuffer)
  Param[1]: width (type: int)
  Param[2]: height (type: int)
Function 307: IsTextureReady() (1 input parameters)
  Name: IsTextureReady
  Return type: bool
  Description: Check if a texture is ready
  Param[1]: texture (type: Texture2D)
Function 308: UnloadTexture() (1 input parameters)
  Name: UnloadTexture
  Return type: void
  Description: Unload texture from GPU memory (VRAM)
  Param[1]: texture (type: Texture2D)
Function 309: IsRenderTextureReady() (1 input parameters)
  Name: IsRenderTextureReady
  Return type: bool
  Description: Check if a render texture is ready
  Param[1]: target (type: RenderTexture2D)
Function 310: UnloadRenderTexture() (1 input parameters)
  Name: UnloadRenderTexture
  Return type: void
  Description: Unload render texture from GPU memory (VRAM)
  Param[1]: target (type: RenderTexture2D)
Function 311: UpdateTexture() (2 input parameters)
  Name: UpdateTexture
  Return type: void
  Description: Update GPU texture with new data
  Param[1]: texture (type: Texture2D)
  Param[2]: pixels (type: const void *)
Function 312: UpdateTextureRec() (3 input parameters)
  Name: UpdateTextureRec
  Return type: void
  Description: Update GPU texture rectangle with new data
  Param[1]: texture (type: Texture2D)
  Param[2]: rec (type: Rectangle)
  Param[3]: pixels (type: const void *)
Function 313: GenTextureMipmaps() (1 input parameters)
  Name: GenTextureMipmaps
  Return type: void
  Description: Generate GPU mipmaps for a texture
  Param[1]: texture (type: Texture2D *)
Function 314: SetTextureFilter() (2 input parameters)
  Name: SetTextureFilter
  Return type: void
  Description: Set texture scaling filter mode
  Param[1]: texture (type: Texture2D)
  Param[2]: filter (type: int)
Function 315: SetTextureWrap() (2 input parameters)
  Name: SetTextureWrap
  Return type: void
  Description: Set texture wrapping mode
  Param[1]: texture (type: Texture2D)
  Param[2]: wrap (type: int)
Function 316: DrawTexture() (4 input parameters)
  Name: DrawTexture
  Return type: void
  Description: Draw a Texture2D
  Param[1]: texture (type: Texture2D)
  Param[2]: posX (type: int)
  Param[3]: posY (type: int)
  Param[4]: tint (type: Color)
Function 317: DrawTextureV() (3 input parameters)
  Name: DrawTextureV
  Return type: void
  Description: Draw a Texture2D with position defined as Vector2
  Param[1]: texture (type: Texture2D)
  Param[2]: position (type: Vector2)
  Param[3]: tint (type: Color)
Function 318: DrawTextureEx() (5 input parameters)
  Name: DrawTextureEx
  Return type: void
  Description: Draw a Texture2D with extended parameters
  Param[1]: texture (type: Texture2D)
  Param[2]: position (type: Vector2)
  Param[3]: rotation (type: float)
  Param[4]: scale (type: float)
  Param[5]: tint (type: Color)
Function 319: DrawTextureRec() (4 input parameters)
  Name: DrawTextureRec
  Return type: void
  Description: Draw a part of a texture defined by a rectangle
  Param[1]: texture (type: Texture2D)
  Param[2]: source (type: Rectangle)
  Param[3]: position (type: Vector2)
  Param[4]: tint (type: Color)
Function 320: DrawTexturePro() (6 input parameters)
  Name: DrawTexturePro
  Return type: void
  Description: Draw a part of a texture defined by a rectangle with 'pro' parameters
  Param[1]: texture (type: Texture2D)
  Param[2]: source (type: Rectangle)
  Param[3]: dest (type: Rectangle)
  Param[4]: origin (type: Vector2)
  Param[5]: rotation (type: float)
  Param[6]: tint (type: Color)
Function 321: DrawTextureNPatch() (6 input parameters)
  Name: DrawTextureNPatch
  Return type: void
  Description: Draws a texture (or part of it) that stretches or shrinks nicely
  Param[1]: texture (type: Texture2D)
  Param[2]: nPatchInfo (type: NPatchInfo)
  Param[3]: dest (type: Rectangle)
  Param[4]: origin (type: Vector2)
  Param[5]: rotation (type: float)
  Param[6]: tint (type: Color)
Function 322: Fade() (2 input parameters)
  Name: Fade
  Return type: Color
  Description: Get color with alpha applied, alpha goes from 0.0f to 1.0f
  Param[1]: color (type: Color)
  Param[2]: alpha (type: float)
Function 323: ColorToInt() (1 input parameters)
  Name: ColorToInt
  Return type: int
  Description: Get hexadecimal value for a Color
  Param[1]: color (type: Color)
Function 324: ColorNormalize() (1 input parameters)
  Name: ColorNormalize
  Return type: Vector4
  Description: Get Color normalized as float [0..1]
  Param[1]: color (type: Color)
Function 325: ColorFromNormalized() (1 input parameters)
  Name: ColorFromNormalized
  Return type: Color
  Description: Get Color from normalized values [0..1]
  Param[1]: normalized (type: Vector4)
Function 326: ColorToHSV() (1 input parameters)
  Name: ColorToHSV
  Return type: Vector3
  Description: Get HSV values for a Color, hue [0..360], saturation/value [0..1]
  Param[1]: color (type: Color)
Function 327: ColorFromHSV() (3 input parameters)
  Name: ColorFromHSV
  Return type: Color
  Description: Get a Color from HSV values, hue [0..360], saturation/value [0..1]
  Param[1]: hue (type: float)
  Param[2]: saturation (type: float)
  Param[3]: value (type: float)
Function 328: ColorTint() (2 input parameters)
  Name: ColorTint
  Return type: Color
  Description: Get color multiplied with another color
  Param[1]: color (type: Color)
  Param[2]: tint (type: Color)
Function 329: ColorBrightness() (2 input parameters)
  Name: ColorBrightness
  Return type: Color
  Description: Get color with brightness correction, brightness factor goes from -1.0f to 1.0f
  Param[1]: color (type: Color)
  Param[2]: factor (type: float)
Function 330: ColorContrast() (2 input parameters)
  Name: ColorContrast
  Return type: Color
  Description: Get color with contrast correction, contrast values between -1.0f and 1.0f
  Param[1]: color (type: Color)
  Param[2]: contrast (type: float)
Function 331: ColorAlpha() (2 input parameters)
  Name: ColorAlpha
  Return type: Color
  Description: Get color with alpha applied, alpha goes from 0.0f to 1.0f
  Param[1]: color (type: Color)
  Param[2]: alpha (type: float)
Function 332: ColorAlphaBlend() (3 input parameters)
  Name: ColorAlphaBlend
  Return type: Color
  Description: Get src alpha-blended into dst color with tint
  Param[1]: dst (type: Color)
  Param[2]: src (type: Color)
  Param[3]: tint (type: Color)
Function 333: GetColor() (1 input parameters)
  Name: GetColor
  Return type: Color
  Description: Get Color structure from hexadecimal value
  Param[1]: hexValue (type: unsigned int)
Function 334: GetPixelColor() (2 input parameters)
  Name: GetPixelColor
  Return type: Color
  Description: Get Color from a source pixel pointer of certain format
  Param[1]: srcPtr (type: void *)
  Param[2]: format (type: int)
Function 335: SetPixelColor() (3 input parameters)
  Name: SetPixelColor
  Return type: void
  Description: Set color formatted into destination pixel pointer
  Param[1]: dstPtr (type: void *)
  Param[2]: color (type: Color)
  Param[3]: format (type: int)
Function 336: GetPixelDataSize() (3 input parameters)
  Name: GetPixelDataSize
  Return type: int
  Description: Get pixel data size in bytes for certain format
  Param[1]: width (type: int)
  Param[2]: height (type: int)
  Param[3]: format (type: int)
Function 337: GetFontDefault() (0 input parameters)
  Name: GetFontDefault
  Return type: Font
  Description: Get the default Font
  No input parameters
Function 338: LoadFont() (1 input parameters)
  Name: LoadFont
  Return type: Font
  Description: Load font from file into GPU memory (VRAM)
  Param[1]: fileName (type: const char *)
Function 339: LoadFontEx() (4 input parameters)
  Name: LoadFontEx
  Return type: Font
  Description: Load font from file with extended parameters, use NULL for fontChars and 0 for glyphCount to load the default character set
  Param[1]: fileName (type: const char *)
  Param[2]: fontSize (type: int)
  Param[3]: fontChars (type: int *)
  Param[4]: glyphCount (type: int)
Function 340: LoadFontFromImage() (3 input parameters)
  Name: LoadFontFromImage
  Return type: Font
  Description: Load font from Image (XNA style)
  Param[1]: image (type: Image)
  Param[2]: key (type: Color)
  Param[3]: firstChar (type: int)
Function 341: LoadFontFromMemory() (6 input parameters)
  Name: LoadFontFromMemory
  Return type: Font
  Description: Load font from memory buffer, fileType refers to extension: i.e. '.ttf'
  Param[1]: fileType (type: const char *)
  Param[2]: fileData (type: const unsigned char *)
  Param[3]: dataSize (type: int)
  Param[4]: fontSize (type: int)
  Param[5]: fontChars (type: int *)
  Param[6]: glyphCount (type: int)
Function 342: IsFontReady() (1 input parameters)
  Name: IsFontReady
  Return type: bool
  Description: Check if a font is ready
  Param[1]: font (type: Font)
Function 343: LoadFontData() (6 input parameters)
  Name: LoadFontData
  Return type: GlyphInfo *
  Description: Load font data for further use
  Param[1]: fileData (type: const unsigned char *)
  Param[2]: dataSize (type: int)
  Param[3]: fontSize (type: int)
  Param[4]: fontChars (type: int *)
  Param[5]: glyphCount (type: int)
  Param[6]: type (type: int)
Function 344: GenImageFontAtlas() (6 input parameters)
  Name: GenImageFontAtlas
  Return type: Image
  Description: Generate image font atlas using chars info
  Param[1]: chars (type: const GlyphInfo *)
  Param[2]: recs (type: Rectangle **)
  Param[3]: glyphCount (type: int)
  Param[4]: fontSize (type: int)
  Param[5]: padding (type: int)
  Param[6]: packMethod (type: int)
Function 345: UnloadFontData() (2 input parameters)
  Name: UnloadFontData
  Return type: void
  Description: Unload font chars info data (RAM)
  Param[1]: chars (type: GlyphInfo *)
  Param[2]: glyphCount (type: int)
Function 346: UnloadFont() (1 input parameters)
  Name: UnloadFont
  Return type: void
  Description: Unload font from GPU memory (VRAM)
  Param[1]: font (type: Font)
Function 347: ExportFontAsCode() (2 input parameters)
  Name: ExportFontAsCode
  Return type: bool
  Description: Export font as code file, returns true on success
  Param[1]: font (type: Font)
  Param[2]: fileName (type: const char *)
Function 348: DrawFPS() (2 input parameters)
  Name: DrawFPS
  Return type: void
  Description: Draw current FPS
  Param[1]: posX (type: int)
  Param[2]: posY (type: int)
Function 349: DrawText() (5 input parameters)
  Name: DrawText
  Return type: void
  Description: Draw text (using default font)
  Param[1]: text (type: const char *)
  Param[2]: posX (type: int)
  Param[3]: posY (type: int)
  Param[4]: fontSize (type: int)
  Param[5]: color (type: Color)
Function 350: DrawTextEx() (6 input parameters)
  Name: DrawTextEx
  Return type: void
  Description: Draw text using font and additional parameters
  Param[1]: font (type: Font)
  Param[2]: text (type: const char *)
  Param[3]: position (type: Vector2)
  Param[4]: fontSize (type: float)
  Param[5]: spacing (type: float)
  Param[6]: tint (type: Color)
Function 351: DrawTextPro() (8 input parameters)
  Name: DrawTextPro
  Return type: void
  Description: Draw text using Font and pro parameters (rotation)
  Param[1]: font (type: Font)
  Param[2]: text (type: const char *)
  Param[3]: position (type: Vector2)
  Param[4]: origin (type: Vector2)
  Param[5]: rotation (type: float)
  Param[6]: fontSize (type: float)
  Param[7]: spacing (type: float)
  Param[8]: tint (type: Color)
Function 352: DrawTextCodepoint() (5 input parameters)
  Name: DrawTextCodepoint
  Return type: void
  Description: Draw one character (codepoint)
  Param[1]: font (type: Font)
  Param[2]: codepoint (type: int)
  Param[3]: position (type: Vector2)
  Param[4]: fontSize (type: float)
  Param[5]: tint (type: Color)
Function 353: DrawTextCodepoints() (7 input parameters)
  Name: DrawTextCodepoints
  Return type: void
  Description: Draw multiple character (codepoint)
  Param[1]: font (type: Font)
  Param[2]: codepoints (type: const int *)
  Param[3]: count (type: int)
  Param[4]: position (type: Vector2)
  Param[5]: fontSize (type: float)
  Param[6]: spacing (type: float)
  Param[7]: tint (type: Color)
Function 354: MeasureText() (2 input parameters)
  Name: MeasureText
  Return type: int
  Description: Measure string width for default font
  Param[1]: text (type: const char *)
  Param[2]: fontSize (type: int)
Function 355: MeasureTextEx() (4 input parameters)
  Name: MeasureTextEx
  Return type: Vector2
  Description: Measure string size for Font
  Param[1]: font (type: Font)
  Param[2]: text (type: const char *)
  Param[3]: fontSize (type: float)
  Param[4]: spacing (type: float)
Function 356: GetGlyphIndex() (2 input parameters)
  Name: GetGlyphIndex
  Return type: int
  Description: Get glyph index position in font for a codepoint (unicode character), fallback to '?' if not found
  Param[1]: font (type: Font)
  Param[2]: codepoint (type: int)
Function 357: GetGlyphInfo() (2 input parameters)
  Name: GetGlyphInfo
  Return type: GlyphInfo
  Description: Get glyph font info data for a codepoint (unicode character), fallback to '?' if not found
  Param[1]: font (type: Font)
  Param[2]: codepoint (type: int)
Function 358: GetGlyphAtlasRec() (2 input parameters)
  Name: GetGlyphAtlasRec
  Return type: Rectangle
  Description: Get glyph rectangle in font atlas for a codepoint (unicode character), fallback to '?' if not found
  Param[1]: font (type: Font)
  Param[2]: codepoint (type: int)
Function 359: LoadUTF8() (2 input parameters)
  Name: LoadUTF8
  Return type: char *
  Description: Load UTF-8 text encoded from codepoints array
  Param[1]: codepoints (type: const int *)
  Param[2]: length (type: int)
Function 360: UnloadUTF8() (1 input parameters)
  Name: UnloadUTF8
  Return type: void
  Description: Unload UTF-8 text encoded from codepoints array
  Param[1]: text (type: char *)
Function 361: LoadCodepoints() (2 input parameters)
  Name: LoadCodepoints
  Return type: int *
  Description: Load all codepoints from a UTF-8 text string, codepoints count returned by parameter
  Param[1]: text (type: const char *)
  Param[2]: count (type: int *)
Function 362: UnloadCodepoints() (1 input parameters)
  Name: UnloadCodepoints
  Return type: void
  Description: Unload codepoints data from memory
  Param[1]: codepoints (type: int *)
Function 363: GetCodepointCount() (1 input parameters)
  Name: GetCodepointCount
  Return type: int
  Description: Get total number of codepoints in a UTF-8 encoded string
  Param[1]: text (type: const char *)
Function 364: GetCodepoint() (2 input parameters)
  Name: GetCodepoint
  Return type: int
  Description: Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure
  Param[1]: text (type: const char *)
  Param[2]: codepointSize (type: int *)
Function 365: GetCodepointNext() (2 input parameters)
  Name: GetCodepointNext
  Return type: int
  Description: Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure
  Param[1]: text (type: const char *)
  Param[2]: codepointSize (type: int *)
Function 366: GetCodepointPrevious() (2 input parameters)
  Name: GetCodepointPrevious
  Return type: int
  Description: Get previous codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure
  Param[1]: text (type: const char *)
  Param[2]: codepointSize (type: int *)
Function 367: CodepointToUTF8() (2 input parameters)
  Name: CodepointToUTF8
  Return type: const char *
  Description: Encode one codepoint into UTF-8 byte array (array length returned as parameter)
  Param[1]: codepoint (type: int)
  Param[2]: utf8Size (type: int *)
Function 368: TextCopy() (2 input parameters)
  Name: TextCopy
  Return type: int
  Description: Copy one string to another, returns bytes copied
  Param[1]: dst (type: char *)
  Param[2]: src (type: const char *)
Function 369: TextIsEqual() (2 input parameters)
  Name: TextIsEqual
  Return type: bool
  Description: Check if two text string are equal
  Param[1]: text1 (type: const char *)
  Param[2]: text2 (type: const char *)
Function 370: TextLength() (1 input parameters)
  Name: TextLength
  Return type: unsigned int
  Description: Get text length, checks for '\0' ending
  Param[1]: text (type: const char *)
Function 371: TextFormat() (2 input parameters)
  Name: TextFormat
  Return type: const char *
  Description: Text formatting with variables (sprintf() style)
  Param[1]: text (type: const char *)
  Param[2]: args (type: ...)
Function 372: TextSubtext() (3 input parameters)
  Name: TextSubtext
  Return type: const char *
  Description: Get a piece of a text string
  Param[1]: text (type: const char *)
  Param[2]: position (type: int)
  Param[3]: length (type: int)
Function 373: TextReplace() (3 input parameters)
  Name: TextReplace
  Return type: char *
  Description: Replace text string (WARNING: memory must be freed!)
  Param[1]: text (type: char *)
  Param[2]: replace (type: const char *)
  Param[3]: by (type: const char *)
Function 374: TextInsert() (3 input parameters)
  Name: TextInsert
  Return type: char *
  Description: Insert text in a position (WARNING: memory must be freed!)
  Param[1]: text (type: const char *)
  Param[2]: insert (type: const char *)
  Param[3]: position (type: int)
Function 375: TextJoin() (3 input parameters)
  Name: TextJoin
  Return type: const char *
  Description: Join text strings with delimiter
  Param[1]: textList (type: const char **)
  Param[2]: count (type: int)
  Param[3]: delimiter (type: const char *)
Function 376: TextSplit() (3 input parameters)
  Name: TextSplit
  Return type: const char **
  Description: Split text into multiple strings
  Param[1]: text (type: const char *)
  Param[2]: delimiter (type: char)
  Param[3]: count (type: int *)
Function 377: TextAppend() (3 input parameters)
  Name: TextAppend
  Return type: void
  Description: Append text at specific position and move cursor!
  Param[1]: text (type: char *)
  Param[2]: append (type: const char *)
  Param[3]: position (type: int *)
Function 378: TextFindIndex() (2 input parameters)
  Name: TextFindIndex
  Return type: int
  Description: Find first text occurrence within a string
  Param[1]: text (type: const char *)
  Param[2]: find (type: const char *)
Function 379: TextToUpper() (1 input parameters)
  Name: TextToUpper
  Return type: const char *
  Description: Get upper case version of provided string
  Param[1]: text (type: const char *)
Function 380: TextToLower() (1 input parameters)
  Name: TextToLower
  Return type: const char *
  Description: Get lower case version of provided string
  Param[1]: text (type: const char *)
Function 381: TextToPascal() (1 input parameters)
  Name: TextToPascal
  Return type: const char *
  Description: Get Pascal case notation version of provided string
  Param[1]: text (type: const char *)
Function 382: TextToInteger() (1 input parameters)
  Name: TextToInteger
  Return type: int
  Description: Get integer value from text (negative values not supported)
  Param[1]: text (type: const char *)
Function 383: DrawLine3D() (3 input parameters)
  Name: DrawLine3D
  Return type: void
  Description: Draw a line in 3D world space
  Param[1]: startPos (type: Vector3)
  Param[2]: endPos (type: Vector3)
  Param[3]: color (type: Color)
Function 384: DrawPoint3D() (2 input parameters)
  Name: DrawPoint3D
  Return type: void
  Description: Draw a point in 3D space, actually a small line
  Param[1]: position (type: Vector3)
  Param[2]: color (type: Color)
Function 385: DrawCircle3D() (5 input parameters)
  Name: DrawCircle3D
  Return type: void
  Description: Draw a circle in 3D world space
  Param[1]: center (type: Vector3)
  Param[2]: radius (type: float)
  Param[3]: rotationAxis (type: Vector3)
  Param[4]: rotationAngle (type: float)
  Param[5]: color (type: Color)
Function 386: DrawTriangle3D() (4 input parameters)
  Name: DrawTriangle3D
  Return type: void
  Description: Draw a color-filled triangle (vertex in counter-clockwise order!)
  Param[1]: v1 (type: Vector3)
  Param[2]: v2 (type: Vector3)
  Param[3]: v3 (type: Vector3)
  Param[4]: color (type: Color)
Function 387: DrawTriangleStrip3D() (3 input parameters)
  Name: DrawTriangleStrip3D
  Return type: void
  Description: Draw a triangle strip defined by points
  Param[1]: points (type: Vector3 *)
  Param[2]: pointCount (type: int)
  Param[3]: color (type: Color)
Function 388: DrawCube() (5 input parameters)
  Name: DrawCube
  Return type: void
  Description: Draw cube
  Param[1]: position (type: Vector3)
  Param[2]: width (type: float)
  Param[3]: height (type: float)
  Param[4]: length (type: float)
  Param[5]: color (type: Color)
Function 389: DrawCubeV() (3 input parameters)
  Name: DrawCubeV
  Return type: void
  Description: Draw cube (Vector version)
  Param[1]: position (type: Vector3)
  Param[2]: size (type: Vector3)
  Param[3]: color (type: Color)
Function 390: DrawCubeWires() (5 input parameters)
  Name: DrawCubeWires
  Return type: void
  Description: Draw cube wires
  Param[1]: position (type: Vector3)
  Param[2]: width (type: float)
  Param[3]: height (type: float)
  Param[4]: length (type: float)
  Param[5]: color (type: Color)
Function 391: DrawCubeWiresV() (3 input parameters)
  Name: DrawCubeWiresV
  Return type: void
  Description: Draw cube wires (Vector version)
  Param[1]: position (type: Vector3)
  Param[2]: size (type: Vector3)
  Param[3]: color (type: Color)
Function 392: DrawSphere() (3 input parameters)
  Name: DrawSphere
  Return type: void
  Description: Draw sphere
  Param[1]: centerPos (type: Vector3)
  Param[2]: radius (type: float)
  Param[3]: color (type: Color)
Function 393: DrawSphereEx() (5 input parameters)
  Name: DrawSphereEx
  Return type: void
  Description: Draw sphere with extended parameters
  Param[1]: centerPos (type: Vector3)
  Param[2]: radius (type: float)
  Param[3]: rings (type: int)
  Param[4]: slices (type: int)
  Param[5]: color (type: Color)
Function 394: DrawSphereWires() (5 input parameters)
  Name: DrawSphereWires
  Return type: void
  Description: Draw sphere wires
  Param[1]: centerPos (type: Vector3)
  Param[2]: radius (type: float)
  Param[3]: rings (type: int)
  Param[4]: slices (type: int)
  Param[5]: color (type: Color)
Function 395: DrawCylinder() (6 input parameters)
  Name: DrawCylinder
  Return type: void
  Description: Draw a cylinder/cone
  Param[1]: position (type: Vector3)
  Param[2]: radiusTop (type: float)
  Param[3]: radiusBottom (type: float)
  Param[4]: height (type: float)
  Param[5]: slices (type: int)
  Param[6]: color (type: Color)
Function 396: DrawCylinderEx() (6 input parameters)
  Name: DrawCylinderEx
  Return type: void
  Description: Draw a cylinder with base at startPos and top at endPos
  Param[1]: startPos (type: Vector3)
  Param[2]: endPos (type: Vector3)
  Param[3]: startRadius (type: float)
  Param[4]: endRadius (type: float)
  Param[5]: sides (type: int)
  Param[6]: color (type: Color)
Function 397: DrawCylinderWires() (6 input parameters)
  Name: DrawCylinderWires
  Return type: void
  Description: Draw a cylinder/cone wires
  Param[1]: position (type: Vector3)
  Param[2]: radiusTop (type: float)
  Param[3]: radiusBottom (type: float)
  Param[4]: height (type: float)
  Param[5]: slices (type: int)
  Param[6]: color (type: Color)
Function 398: DrawCylinderWiresEx() (6 input parameters)
  Name: DrawCylinderWiresEx
  Return type: void
  Description: Draw a cylinder wires with base at startPos and top at endPos
  Param[1]: startPos (type: Vector3)
  Param[2]: endPos (type: Vector3)
  Param[3]: startRadius (type: float)
  Param[4]: endRadius (type: float)
  Param[5]: sides (type: int)
  Param[6]: color (type: Color)
Function 399: DrawCapsule() (6 input parameters)
  Name: DrawCapsule
  Return type: void
  Description: Draw a capsule with the center of its sphere caps at startPos and endPos
  Param[1]: startPos (type: Vector3)
  Param[2]: endPos (type: Vector3)
  Param[3]: radius (type: float)
  Param[4]: slices (type: int)
  Param[5]: rings (type: int)
  Param[6]: color (type: Color)
Function 400: DrawCapsuleWires() (6 input parameters)
  Name: DrawCapsuleWires
  Return type: void
  Description: Draw capsule wireframe with the center of its sphere caps at startPos and endPos
  Param[1]: startPos (type: Vector3)
  Param[2]: endPos (type: Vector3)
  Param[3]: radius (type: float)
  Param[4]: slices (type: int)
  Param[5]: rings (type: int)
  Param[6]: color (type: Color)
Function 401: DrawPlane() (3 input parameters)
  Name: DrawPlane
  Return type: void
  Description: Draw a plane XZ
  Param[1]: centerPos (type: Vector3)
  Param[2]: size (type: Vector2)
  Param[3]: color (type: Color)
Function 402: DrawRay() (2 input parameters)
  Name: DrawRay
  Return type: void
  Description: Draw a ray line
  Param[1]: ray (type: Ray)
  Param[2]: color (type: Color)
Function 403: DrawGrid() (2 input parameters)
  Name: DrawGrid
  Return type: void
  Description: Draw a grid (centered at (0, 0, 0))
  Param[1]: slices (type: int)
  Param[2]: spacing (type: float)
Function 404: LoadModel() (1 input parameters)
  Name: LoadModel
  Return type: Model
  Description: Load model from files (meshes and materials)
  Param[1]: fileName (type: const char *)
Function 405: LoadModelFromMesh() (1 input parameters)
  Name: LoadModelFromMesh
  Return type: Model
  Description: Load model from generated mesh (default material)
  Param[1]: mesh (type: Mesh)
Function 406: IsModelReady() (1 input parameters)
  Name: IsModelReady
  Return type: bool
  Description: Check if a model is ready
  Param[1]: model (type: Model)
Function 407: UnloadModel() (1 input parameters)
  Name: UnloadModel
  Return type: void
  Description: Unload model (including meshes) from memory (RAM and/or VRAM)
  Param[1]: model (type: Model)
Function 408: GetModelBoundingBox() (1 input parameters)
  Name: GetModelBoundingBox
  Return type: BoundingBox
  Description: Compute model bounding box limits (considers all meshes)
  Param[1]: model (type: Model)
Function 409: DrawModel() (4 input parameters)
  Name: DrawModel
  Return type: void
  Description: Draw a model (with texture if set)
  Param[1]: model (type: Model)
  Param[2]: position (type: Vector3)
  Param[3]: scale (type: float)
  Param[4]: tint (type: Color)
Function 410: DrawModelEx() (6 input parameters)
  Name: DrawModelEx
  Return type: void
  Description: Draw a model with extended parameters
  Param[1]: model (type: Model)
  Param[2]: position (type: Vector3)
  Param[3]: rotationAxis (type: Vector3)
  Param[4]: rotationAngle (type: float)
  Param[5]: scale (type: Vector3)
  Param[6]: tint (type: Color)
Function 411: DrawModelWires() (4 input parameters)
  Name: DrawModelWires
  Return type: void
  Description: Draw a model wires (with texture if set)
  Param[1]: model (type: Model)
  Param[2]: position (type: Vector3)
  Param[3]: scale (type: float)
  Param[4]: tint (type: Color)
Function 412: DrawModelWiresEx() (6 input parameters)
  Name: DrawModelWiresEx
  Return type: void
  Description: Draw a model wires (with texture if set) with extended parameters
  Param[1]: model (type: Model)
  Param[2]: position (type: Vector3)
  Param[3]: rotationAxis (type: Vector3)
  Param[4]: rotationAngle (type: float)
  Param[5]: scale (type: Vector3)
  Param[6]: tint (type: Color)
Function 413: DrawBoundingBox() (2 input parameters)
  Name: DrawBoundingBox
  Return type: void
  Description: Draw bounding box (wires)
  Param[1]: box (type: BoundingBox)
  Param[2]: color (type: Color)
Function 414: DrawBillboard() (5 input parameters)
  Name: DrawBillboard
  Return type: void
  Description: Draw a billboard texture
  Param[1]: camera (type: Camera)
  Param[2]: texture (type: Texture2D)
  Param[3]: position (type: Vector3)
  Param[4]: size (type: float)
  Param[5]: tint (type: Color)
Function 415: DrawBillboardRec() (6 input parameters)
  Name: DrawBillboardRec
  Return type: void
  Description: Draw a billboard texture defined by source
  Param[1]: camera (type: Camera)
  Param[2]: texture (type: Texture2D)
  Param[3]: source (type: Rectangle)
  Param[4]: position (type: Vector3)
  Param[5]: size (type: Vector2)
  Param[6]: tint (type: Color)
Function 416: DrawBillboardPro() (9 input parameters)
  Name: DrawBillboardPro
  Return type: void
  Description: Draw a billboard texture defined by source and rotation
  Param[1]: camera (type: Camera)
  Param[2]: texture (type: Texture2D)
  Param[3]: source (type: Rectangle)
  Param[4]: position (type: Vector3)
  Param[5]: up (type: Vector3)
  Param[6]: size (type: Vector2)
  Param[7]: origin (type: Vector2)
  Param[8]: rotation (type: float)
  Param[9]: tint (type: Color)
Function 417: UploadMesh() (2 input parameters)
  Name: UploadMesh
  Return type: void
  Description: Upload mesh vertex data in GPU and provide VAO/VBO ids
  Param[1]: mesh (type: Mesh *)
  Param[2]: dynamic (type: bool)
Function 418: UpdateMeshBuffer() (5 input parameters)
  Name: UpdateMeshBuffer
  Return type: void
  Description: Update mesh vertex data in GPU for a specific buffer index
  Param[1]: mesh (type: Mesh)
  Param[2]: index (type: int)
  Param[3]: data (type: const void *)
  Param[4]: dataSize (type: int)
  Param[5]: offset (type: int)
Function 419: UnloadMesh() (1 input parameters)
  Name: UnloadMesh
  Return type: void
  Description: Unload mesh data from CPU and GPU
  Param[1]: mesh (type: Mesh)
Function 420: DrawMesh() (3 input parameters)
  Name: DrawMesh
  Return type: void
  Description: Draw a 3d mesh with material and transform
  Param[1]: mesh (type: Mesh)
  Param[2]: material (type: Material)
  Param[3]: transform (type: Matrix)
Function 421: DrawMeshInstanced() (4 input parameters)
  Name: DrawMeshInstanced
  Return type: void
  Description: Draw multiple mesh instances with material and different transforms
  Param[1]: mesh (type: Mesh)
  Param[2]: material (type: Material)
  Param[3]: transforms (type: const Matrix *)
  Param[4]: instances (type: int)
Function 422: ExportMesh() (2 input parameters)
  Name: ExportMesh
  Return type: bool
  Description: Export mesh data to file, returns true on success
  Param[1]: mesh (type: Mesh)
  Param[2]: fileName (type: const char *)
Function 423: GetMeshBoundingBox() (1 input parameters)
  Name: GetMeshBoundingBox
  Return type: BoundingBox
  Description: Compute mesh bounding box limits
  Param[1]: mesh (type: Mesh)
Function 424: GenMeshTangents() (1 input parameters)
  Name: GenMeshTangents
  Return type: void
  Description: Compute mesh tangents
  Param[1]: mesh (type: Mesh *)
Function 425: GenMeshPoly() (2 input parameters)
  Name: GenMeshPoly
  Return type: Mesh
  Description: Generate polygonal mesh
  Param[1]: sides (type: int)
  Param[2]: radius (type: float)
Function 426: GenMeshPlane() (4 input parameters)
  Name: GenMeshPlane
  Return type: Mesh
  Description: Generate plane mesh (with subdivisions)
  Param[1]: width (type: float)
  Param[2]: length (type: float)
  Param[3]: resX (type: int)
  Param[4]: resZ (type: int)
Function 427: GenMeshCube() (3 input parameters)
  Name: GenMeshCube
  Return type: Mesh
  Description: Generate cuboid mesh
  Param[1]: width (type: float)
  Param[2]: height (type: float)
  Param[3]: length (type: float)
Function 428: GenMeshSphere() (3 input parameters)
  Name: GenMeshSphere
  Return type: Mesh
  Description: Generate sphere mesh (standard sphere)
  Param[1]: radius (type: float)
  Param[2]: rings (type: int)
  Param[3]: slices (type: int)
Function 429: GenMeshHemiSphere() (3 input parameters)
  Name: GenMeshHemiSphere
  Return type: Mesh
  Description: Generate half-sphere mesh (no bottom cap)
  Param[1]: radius (type: float)
  Param[2]: rings (type: int)
  Param[3]: slices (type: int)
Function 430: GenMeshCylinder() (3 input parameters)
  Name: GenMeshCylinder
  Return type: Mesh
  Description: Generate cylinder mesh
  Param[1]: radius (type: float)
  Param[2]: height (type: float)
  Param[3]: slices (type: int)
Function 431: GenMeshCone() (3 input parameters)
  Name: GenMeshCone
  Return type: Mesh
  Description: Generate cone/pyramid mesh
  Param[1]: radius (type: float)
  Param[2]: height (type: float)
  Param[3]: slices (type: int)
Function 432: GenMeshTorus() (4 input parameters)
  Name: GenMeshTorus
  Return type: Mesh
  Description: Generate torus mesh
  Param[1]: radius (type: float)
  Param[2]: size (type: float)
  Param[3]: radSeg (type: int)
  Param[4]: sides (type: int)
Function 433: GenMeshKnot() (4 input parameters)
  Name: GenMeshKnot
  Return type: Mesh
  Description: Generate trefoil knot mesh
  Param[1]: radius (type: float)
  Param[2]: size (type: float)
  Param[3]: radSeg (type: int)
  Param[4]: sides (type: int)
Function 434: GenMeshHeightmap() (2 input parameters)
  Name: GenMeshHeightmap
  Return type: Mesh
  Description: Generate heightmap mesh from image data
  Param[1]: heightmap (type: Image)
  Param[2]: size (type: Vector3)
Function 435: GenMeshCubicmap() (2 input parameters)
  Name: GenMeshCubicmap
  Return type: Mesh
  Description: Generate cubes-based map mesh from image data
  Param[1]: cubicmap (type: Image)
  Param[2]: cubeSize (type: Vector3)
Function 436: LoadMaterials() (2 input parameters)
  Name: LoadMaterials
  Return type: Material *
  Description: Load materials from model file
  Param[1]: fileName (type: const char *)
  Param[2]: materialCount (type: int *)
Function 437: LoadMaterialDefault() (0 input parameters)
  Name: LoadMaterialDefault
  Return type: Material
  Description: Load default material (Supports: DIFFUSE, SPECULAR, NORMAL maps)
  No input parameters
Function 438: IsMaterialReady() (1 input parameters)
  Name: IsMaterialReady
  Return type: bool
  Description: Check if a material is ready
  Param[1]: material (type: Material)
Function 439: UnloadMaterial() (1 input parameters)
  Name: UnloadMaterial
  Return type: void
  Description: Unload material from GPU memory (VRAM)
  Param[1]: material (type: Material)
Function 440: SetMaterialTexture() (3 input parameters)
  Name: SetMaterialTexture
  Return type: void
  Description: Set texture for a material map type (MATERIAL_MAP_DIFFUSE, MATERIAL_MAP_SPECULAR...)
  Param[1]: material (type: Material *)
  Param[2]: mapType (type: int)
  Param[3]: texture (type: Texture2D)
Function 441: SetModelMeshMaterial() (3 input parameters)
  Name: SetModelMeshMaterial
  Return type: void
  Description: Set material for a mesh
  Param[1]: model (type: Model *)
  Param[2]: meshId (type: int)
  Param[3]: materialId (type: int)
Function 442: LoadModelAnimations() (2 input parameters)
  Name: LoadModelAnimations
  Return type: ModelAnimation *
  Description: Load model animations from file
  Param[1]: fileName (type: const char *)
  Param[2]: animCount (type: unsigned int *)
Function 443: UpdateModelAnimation() (3 input parameters)
  Name: UpdateModelAnimation
  Return type: void
  Description: Update model animation pose
  Param[1]: model (type: Model)
  Param[2]: anim (type: ModelAnimation)
  Param[3]: frame (type: int)
Function 444: UnloadModelAnimation() (1 input parameters)
  Name: UnloadModelAnimation
  Return type: void
  Description: Unload animation data
  Param[1]: anim (type: ModelAnimation)
Function 445: UnloadModelAnimations() (2 input parameters)
  Name: UnloadModelAnimations
  Return type: void
  Description: Unload animation array data
  Param[1]: animations (type: ModelAnimation *)
  Param[2]: count (type: unsigned int)
Function 446: IsModelAnimationValid() (2 input parameters)
  Name: IsModelAnimationValid
  Return type: bool
  Description: Check model animation skeleton match
  Param[1]: model (type: Model)
  Param[2]: anim (type: ModelAnimation)
Function 447: CheckCollisionSpheres() (4 input parameters)
  Name: CheckCollisionSpheres
  Return type: bool
  Description: Check collision between two spheres
  Param[1]: center1 (type: Vector3)
  Param[2]: radius1 (type: float)
  Param[3]: center2 (type: Vector3)
  Param[4]: radius2 (type: float)
Function 448: CheckCollisionBoxes() (2 input parameters)
  Name: CheckCollisionBoxes
  Return type: bool
  Description: Check collision between two bounding boxes
  Param[1]: box1 (type: BoundingBox)
  Param[2]: box2 (type: BoundingBox)
Function 449: CheckCollisionBoxSphere() (3 input parameters)
  Name: CheckCollisionBoxSphere
  Return type: bool
  Description: Check collision between box and sphere
  Param[1]: box (type: BoundingBox)
  Param[2]: center (type: Vector3)
  Param[3]: radius (type: float)
Function 450: GetRayCollisionSphere() (3 input parameters)
  Name: GetRayCollisionSphere
  Return type: RayCollision
  Description: Get collision info between ray and sphere
  Param[1]: ray (type: Ray)
  Param[2]: center (type: Vector3)
  Param[3]: radius (type: float)
Function 451: GetRayCollisionBox() (2 input parameters)
  Name: GetRayCollisionBox
  Return type: RayCollision
  Description: Get collision info between ray and box
  Param[1]: ray (type: Ray)
  Param[2]: box (type: BoundingBox)
Function 452: GetRayCollisionMesh() (3 input parameters)
  Name: GetRayCollisionMesh
  Return type: RayCollision
  Description: Get collision info between ray and mesh
  Param[1]: ray (type: Ray)
  Param[2]: mesh (type: Mesh)
  Param[3]: transform (type: Matrix)
Function 453: GetRayCollisionTriangle() (4 input parameters)
  Name: GetRayCollisionTriangle
  Return type: RayCollision
  Description: Get collision info between ray and triangle
  Param[1]: ray (type: Ray)
  Param[2]: p1 (type: Vector3)
  Param[3]: p2 (type: Vector3)
  Param[4]: p3 (type: Vector3)
Function 454: GetRayCollisionQuad() (5 input parameters)
  Name: GetRayCollisionQuad
  Return type: RayCollision
  Description: Get collision info between ray and quad
  Param[1]: ray (type: Ray)
  Param[2]: p1 (type: Vector3)
  Param[3]: p2 (type: Vector3)
  Param[4]: p3 (type: Vector3)
  Param[5]: p4 (type: Vector3)
Function 455: InitAudioDevice() (0 input parameters)
  Name: InitAudioDevice
  Return type: void
  Description: Initialize audio device and context
  No input parameters
Function 456: CloseAudioDevice() (0 input parameters)
  Name: CloseAudioDevice
  Return type: void
  Description: Close the audio device and context
  No input parameters
Function 457: IsAudioDeviceReady() (0 input parameters)
  Name: IsAudioDeviceReady
  Return type: bool
  Description: Check if audio device has been initialized successfully
  No input parameters
Function 458: SetMasterVolume() (1 input parameters)
  Name: SetMasterVolume
  Return type: void
  Description: Set master volume (listener)
  Param[1]: volume (type: float)
Function 459: LoadWave() (1 input parameters)
  Name: LoadWave
  Return type: Wave
  Description: Load wave data from file
  Param[1]: fileName (type: const char *)
Function 460: LoadWaveFromMemory() (3 input parameters)
  Name: LoadWaveFromMemory
  Return type: Wave
  Description: Load wave from memory buffer, fileType refers to extension: i.e. '.wav'
  Param[1]: fileType (type: const char *)
  Param[2]: fileData (type: const unsigned char *)
  Param[3]: dataSize (type: int)
Function 461: IsWaveReady() (1 input parameters)
  Name: IsWaveReady
  Return type: bool
  Description: Checks if wave data is ready
  Param[1]: wave (type: Wave)
Function 462: LoadSound() (1 input parameters)
  Name: LoadSound
  Return type: Sound
  Description: Load sound from file
  Param[1]: fileName (type: const char *)
Function 463: LoadSoundFromWave() (1 input parameters)
  Name: LoadSoundFromWave
  Return type: Sound
  Description: Load sound from wave data
  Param[1]: wave (type: Wave)
Function 464: IsSoundReady() (1 input parameters)
  Name: IsSoundReady
  Return type: bool
  Description: Checks if a sound is ready
  Param[1]: sound (type: Sound)
Function 465: UpdateSound() (3 input parameters)
  Name: UpdateSound
  Return type: void
  Description: Update sound buffer with new data
  Param[1]: sound (type: Sound)
  Param[2]: data (type: const void *)
  Param[3]: sampleCount (type: int)
Function 466: UnloadWave() (1 input parameters)
  Name: UnloadWave
  Return type: void
  Description: Unload wave data
  Param[1]: wave (type: Wave)
Function 467: UnloadSound() (1 input parameters)
  Name: UnloadSound
  Return type: void
  Description: Unload sound
  Param[1]: sound (type: Sound)
Function 468: ExportWave() (2 input parameters)
  Name: ExportWave
  Return type: bool
  Description: Export wave data to file, returns true on success
  Param[1]: wave (type: Wave)
  Param[2]: fileName (type: const char *)
Function 469: ExportWaveAsCode() (2 input parameters)
  Name: ExportWaveAsCode
  Return type: bool
  Description: Export wave sample data to code (.h), returns true on success
  Param[1]: wave (type: Wave)
  Param[2]: fileName (type: const char *)
Function 470: PlaySound() (1 input parameters)
  Name: PlaySound
  Return type: void
  Description: Play a sound
  Param[1]: sound (type: Sound)
Function 471: StopSound() (1 input parameters)
  Name: StopSound
  Return type: void
  Description: Stop playing a sound
  Param[1]: sound (type: Sound)
Function 472: PauseSound() (1 input parameters)
  Name: PauseSound
  Return type: void
  Description: Pause a sound
  Param[1]: sound (type: Sound)
Function 473: ResumeSound() (1 input parameters)
  Name: ResumeSound
  Return type: void
  Description: Resume a paused sound
  Param[1]: sound (type: Sound)
Function 474: IsSoundPlaying() (1 input parameters)
  Name: IsSoundPlaying
  Return type: bool
  Description: Check if a sound is currently playing
  Param[1]: sound (type: Sound)
Function 475: SetSoundVolume() (2 input parameters)
  Name: SetSoundVolume
  Return type: void
  Description: Set volume for a sound (1.0 is max level)
  Param[1]: sound (type: Sound)
  Param[2]: volume (type: float)
Function 476: SetSoundPitch() (2 input parameters)
  Name: SetSoundPitch
  Return type: void
  Description: Set pitch for a sound (1.0 is base level)
  Param[1]: sound (type: Sound)
  Param[2]: pitch (type: float)
Function 477: SetSoundPan() (2 input parameters)
  Name: SetSoundPan
  Return type: void
  Description: Set pan for a sound (0.5 is center)
  Param[1]: sound (type: Sound)
  Param[2]: pan (type: float)
Function 478: WaveCopy() (1 input parameters)
  Name: WaveCopy
  Return type: Wave
  Description: Copy a wave to a new wave
  Param[1]: wave (type: Wave)
Function 479: WaveCrop() (3 input parameters)
  Name: WaveCrop
  Return type: void
  Description: Crop a wave to defined samples range
  Param[1]: wave (type: Wave *)
  Param[2]: initSample (type: int)
  Param[3]: finalSample (type: int)
Function 480: WaveFormat() (4 input parameters)
  Name: WaveFormat
  Return type: void
  Description: Convert wave data to desired format
  Param[1]: wave (type: Wave *)
  Param[2]: sampleRate (type: int)
  Param[3]: sampleSize (type: int)
  Param[4]: channels (type: int)
Function 481: LoadWaveSamples() (1 input parameters)
  Name: LoadWaveSamples
  Return type: float *
  Description: Load samples data from wave as a 32bit float data array
  Param[1]: wave (type: Wave)
Function 482: UnloadWaveSamples() (1 input parameters)
  Name: UnloadWaveSamples
  Return type: void
  Description: Unload samples data loaded with LoadWaveSamples()
  Param[1]: samples (type: float *)
Function 483: LoadMusicStream() (1 input parameters)
  Name: LoadMusicStream
  Return type: Music
  Description: Load music stream from file
  Param[1]: fileName (type: const char *)
Function 484: LoadMusicStreamFromMemory() (3 input parameters)
  Name: LoadMusicStreamFromMemory
  Return type: Music
  Description: Load music stream from data
  Param[1]: fileType (type: const char *)
  Param[2]: data (type: const unsigned char *)
  Param[3]: dataSize (type: int)
Function 485: IsMusicReady() (1 input parameters)
  Name: IsMusicReady
  Return type: bool
  Description: Checks if a music stream is ready
  Param[1]: music (type: Music)
Function 486: UnloadMusicStream() (1 input parameters)
  Name: UnloadMusicStream
  Return type: void
  Description: Unload music stream
  Param[1]: music (type: Music)
Function 487: PlayMusicStream() (1 input parameters)
  Name: PlayMusicStream
  Return type: void
  Description: Start music playing
  Param[1]: music (type: Music)
Function 488: IsMusicStreamPlaying() (1 input parameters)
  Name: IsMusicStreamPlaying
  Return type: bool
  Description: Check if music is playing
  Param[1]: music (type: Music)
Function 489: UpdateMusicStream() (1 input parameters)
  Name: UpdateMusicStream
  Return type: void
  Description: Updates buffers for music streaming
  Param[1]: music (type: Music)
Function 490: StopMusicStream() (1 input parameters)
  Name: StopMusicStream
  Return type: void
  Description: Stop music playing
  Param[1]: music (type: Music)
Function 491: PauseMusicStream() (1 input parameters)
  Name: PauseMusicStream
  Return type: void
  Description: Pause music playing
  Param[1]: music (type: Music)
Function 492: ResumeMusicStream() (1 input parameters)
  Name: ResumeMusicStream
  Return type: void
  Description: Resume playing paused music
  Param[1]: music (type: Music)
Function 493: SeekMusicStream() (2 input parameters)
  Name: SeekMusicStream
  Return type: void
  Description: Seek music to a position (in seconds)
  Param[1]: music (type: Music)
  Param[2]: position (type: float)
Function 494: SetMusicVolume() (2 input parameters)
  Name: SetMusicVolume
  Return type: void
  Description: Set volume for music (1.0 is max level)
  Param[1]: music (type: Music)
  Param[2]: volume (type: float)
Function 495: SetMusicPitch() (2 input parameters)
  Name: SetMusicPitch
  Return type: void
  Description: Set pitch for a music (1.0 is base level)
  Param[1]: music (type: Music)
  Param[2]: pitch (type: float)
Function 496: SetMusicPan() (2 input parameters)
  Name: SetMusicPan
  Return type: void
  Description: Set pan for a music (0.5 is center)
  Param[1]: music (type: Music)
  Param[2]: pan (type: float)
Function 497: GetMusicTimeLength() (1 input parameters)
  Name: GetMusicTimeLength
  Return type: float
  Description: Get music time length (in seconds)
  Param[1]: music (type: Music)
Function 498: GetMusicTimePlayed() (1 input parameters)
  Name: GetMusicTimePlayed
  Return type: float
  Description: Get current music time played (in seconds)
  Param[1]: music (type: Music)
Function 499: LoadAudioStream() (3 input parameters)
  Name: LoadAudioStream
  Return type: AudioStream
  Description: Load audio stream (to stream raw audio pcm data)
  Param[1]: sampleRate (type: unsigned int)
  Param[2]: sampleSize (type: unsigned int)
  Param[3]: channels (type: unsigned int)
Function 500: IsAudioStreamReady() (1 input parameters)
  Name: IsAudioStreamReady
  Return type: bool
  Description: Checks if an audio stream is ready
  Param[1]: stream (type: AudioStream)
Function 501: UnloadAudioStream() (1 input parameters)
  Name: UnloadAudioStream
  Return type: void
  Description: Unload audio stream and free memory
  Param[1]: stream (type: AudioStream)
Function 502: UpdateAudioStream() (3 input parameters)
  Name: UpdateAudioStream
  Return type: void
  Description: Update audio stream buffers with data
  Param[1]: stream (type: AudioStream)
  Param[2]: data (type: const void *)
  Param[3]: frameCount (type: int)
Function 503: IsAudioStreamProcessed() (1 input parameters)
  Name: IsAudioStreamProcessed
  Return type: bool
  Description: Check if any audio stream buffers requires refill
  Param[1]: stream (type: AudioStream)
Function 504: PlayAudioStream() (1 input parameters)
  Name: PlayAudioStream
  Return type: void
  Description: Play audio stream
  Param[1]: stream (type: AudioStream)
Function 505: PauseAudioStream() (1 input parameters)
  Name: PauseAudioStream
  Return type: void
  Description: Pause audio stream
  Param[1]: stream (type: AudioStream)
Function 506: ResumeAudioStream() (1 input parameters)
  Name: ResumeAudioStream
  Return type: void
  Description: Resume audio stream
  Param[1]: stream (type: AudioStream)
Function 507: IsAudioStreamPlaying() (1 input parameters)
  Name: IsAudioStreamPlaying
  Return type: bool
  Description: Check if audio stream is playing
  Param[1]: stream (type: AudioStream)
Function 508: StopAudioStream() (1 input parameters)
  Name: StopAudioStream
  Return type: void
  Description: Stop audio stream
  Param[1]: stream (type: AudioStream)
Function 509: SetAudioStreamVolume() (2 input parameters)
  Name: SetAudioStreamVolume
  Return type: void
  Description: Set volume for audio stream (1.0 is max level)
  Param[1]: stream (type: AudioStream)
  Param[2]: volume (type: float)
Function 510: SetAudioStreamPitch() (2 input parameters)
  Name: SetAudioStreamPitch
  Return type: void
  Description: Set pitch for audio stream (1.0 is base level)
  Param[1]: stream (type: AudioStream)
  Param[2]: pitch (type: float)
Function 511: SetAudioStreamPan() (2 input parameters)
  Name: SetAudioStreamPan
  Return type: void
  Description: Set pan for audio stream (0.5 is centered)
  Param[1]: stream (type: AudioStream)
  Param[2]: pan (type: float)
Function 512: SetAudioStreamBufferSizeDefault() (1 input parameters)
  Name: SetAudioStreamBufferSizeDefault
  Return type: void
  Description: Default size for new audio streams
  Param[1]: size (type: int)
Function 513: SetAudioStreamCallback() (2 input parameters)
  Name: SetAudioStreamCallback
  Return type: void
  Description: Audio thread callback to request new data
  Param[1]: stream (type: AudioStream)
  Param[2]: callback (type: AudioCallback)
Function 514: AttachAudioStreamProcessor() (2 input parameters)
  Name: AttachAudioStreamProcessor
  Return type: void
  Description: Attach audio stream processor to stream
  Param[1]: stream (type: AudioStream)
  Param[2]: processor (type: AudioCallback)
Function 515: DetachAudioStreamProcessor() (2 input parameters)
  Name: DetachAudioStreamProcessor
  Return type: void
  Description: Detach audio stream processor from stream
  Param[1]: stream (type: AudioStream)
  Param[2]: processor (type: AudioCallback)
Function 516: AttachAudioMixedProcessor() (1 input parameters)
  Name: AttachAudioMixedProcessor
  Return type: void
  Description: Attach audio stream processor to the entire audio pipeline
  Param[1]: processor (type: AudioCallback)
Function 517: DetachAudioMixedProcessor() (1 input parameters)
  Name: DetachAudioMixedProcessor
  Return type: void
  Description: Detach audio stream processor from the entire audio pipeline
  Param[1]: processor (type: AudioCallback)
