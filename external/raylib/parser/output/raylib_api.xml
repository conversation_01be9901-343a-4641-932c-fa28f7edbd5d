<?xml version="1.0" encoding="Windows-1252" ?>
<raylibAPI>
    <Defines count="56">
        <Define name="RAYLIB_H" type="GUARD" value="" desc="" />
        <Define name="RAYLIB_VERSION_MAJOR" type="INT" value="4" desc="" />
        <Define name="RAYLIB_VERSION_MINOR" type="INT" value="5" desc="" />
        <Define name="RAYLIB_VERSION_PATCH" type="INT" value="0" desc="" />
        <Define name="RAYLIB_VERSION" type="STRING" value="4.5" desc="" />
        <Define name="__declspec(x)" type="MACRO" value="__attribute__((x))" desc="" />
        <Define name="RLAPI" type="UNKNOWN" value="__declspec(dllexport)" desc="We are building the library as a Win32 shared library (.dll)" />
        <Define name="PI" type="FLOAT" value="3.14159265358979323846" desc="" />
        <Define name="DEG2RAD" type="FLOAT_MATH" value="(PI/180.0f)" desc="" />
        <Define name="RAD2DEG" type="FLOAT_MATH" value="(180.0f/PI)" desc="" />
        <Define name="RL_MALLOC(sz)" type="MACRO" value="malloc(sz)" desc="" />
        <Define name="RL_CALLOC(n,sz)" type="MACRO" value="calloc(n,sz)" desc="" />
        <Define name="RL_REALLOC(ptr,sz)" type="MACRO" value="realloc(ptr,sz)" desc="" />
        <Define name="RL_FREE(ptr)" type="MACRO" value="free(ptr)" desc="" />
        <Define name="CLITERAL(type)" type="MACRO" value="type" desc="" />
        <Define name="RL_COLOR_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_RECTANGLE_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_VECTOR2_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_VECTOR3_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_VECTOR4_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_QUATERNION_TYPE" type="GUARD" value="" desc="" />
        <Define name="RL_MATRIX_TYPE" type="GUARD" value="" desc="" />
        <Define name="LIGHTGRAY" type="COLOR" value="CLITERAL(Color){ 200, 200, 200, 255 }" desc="Light Gray" />
        <Define name="GRAY" type="COLOR" value="CLITERAL(Color){ 130, 130, 130, 255 }" desc="Gray" />
        <Define name="DARKGRAY" type="COLOR" value="CLITERAL(Color){ 80, 80, 80, 255 }" desc="Dark Gray" />
        <Define name="YELLOW" type="COLOR" value="CLITERAL(Color){ 253, 249, 0, 255 }" desc="Yellow" />
        <Define name="GOLD" type="COLOR" value="CLITERAL(Color){ 255, 203, 0, 255 }" desc="Gold" />
        <Define name="ORANGE" type="COLOR" value="CLITERAL(Color){ 255, 161, 0, 255 }" desc="Orange" />
        <Define name="PINK" type="COLOR" value="CLITERAL(Color){ 255, 109, 194, 255 }" desc="Pink" />
        <Define name="RED" type="COLOR" value="CLITERAL(Color){ 230, 41, 55, 255 }" desc="Red" />
        <Define name="MAROON" type="COLOR" value="CLITERAL(Color){ 190, 33, 55, 255 }" desc="Maroon" />
        <Define name="GREEN" type="COLOR" value="CLITERAL(Color){ 0, 228, 48, 255 }" desc="Green" />
        <Define name="LIME" type="COLOR" value="CLITERAL(Color){ 0, 158, 47, 255 }" desc="Lime" />
        <Define name="DARKGREEN" type="COLOR" value="CLITERAL(Color){ 0, 117, 44, 255 }" desc="Dark Green" />
        <Define name="SKYBLUE" type="COLOR" value="CLITERAL(Color){ 102, 191, 255, 255 }" desc="Sky Blue" />
        <Define name="BLUE" type="COLOR" value="CLITERAL(Color){ 0, 121, 241, 255 }" desc="Blue" />
        <Define name="DARKBLUE" type="COLOR" value="CLITERAL(Color){ 0, 82, 172, 255 }" desc="Dark Blue" />
        <Define name="PURPLE" type="COLOR" value="CLITERAL(Color){ 200, 122, 255, 255 }" desc="Purple" />
        <Define name="VIOLET" type="COLOR" value="CLITERAL(Color){ 135, 60, 190, 255 }" desc="Violet" />
        <Define name="DARKPURPLE" type="COLOR" value="CLITERAL(Color){ 112, 31, 126, 255 }" desc="Dark Purple" />
        <Define name="BEIGE" type="COLOR" value="CLITERAL(Color){ 211, 176, 131, 255 }" desc="Beige" />
        <Define name="BROWN" type="COLOR" value="CLITERAL(Color){ 127, 106, 79, 255 }" desc="Brown" />
        <Define name="DARKBROWN" type="COLOR" value="CLITERAL(Color){ 76, 63, 47, 255 }" desc="Dark Brown" />
        <Define name="WHITE" type="COLOR" value="CLITERAL(Color){ 255, 255, 255, 255 }" desc="White" />
        <Define name="BLACK" type="COLOR" value="CLITERAL(Color){ 0, 0, 0, 255 }" desc="Black" />
        <Define name="BLANK" type="COLOR" value="CLITERAL(Color){ 0, 0, 0, 0 }" desc="Blank (Transparent)" />
        <Define name="MAGENTA" type="COLOR" value="CLITERAL(Color){ 255, 0, 255, 255 }" desc="Magenta" />
        <Define name="RAYWHITE" type="COLOR" value="CLITERAL(Color){ 245, 245, 245, 255 }" desc="My own White (raylib logo)" />
        <Define name="RL_BOOL_TYPE" type="GUARD" value="" desc="" />
        <Define name="MOUSE_LEFT_BUTTON" type="UNKNOWN" value="MOUSE_BUTTON_LEFT" desc="" />
        <Define name="MOUSE_RIGHT_BUTTON" type="UNKNOWN" value="MOUSE_BUTTON_RIGHT" desc="" />
        <Define name="MOUSE_MIDDLE_BUTTON" type="UNKNOWN" value="MOUSE_BUTTON_MIDDLE" desc="" />
        <Define name="MATERIAL_MAP_DIFFUSE" type="UNKNOWN" value="MATERIAL_MAP_ALBEDO" desc="" />
        <Define name="MATERIAL_MAP_SPECULAR" type="UNKNOWN" value="MATERIAL_MAP_METALNESS" desc="" />
        <Define name="SHADER_LOC_MAP_DIFFUSE" type="UNKNOWN" value="SHADER_LOC_MAP_ALBEDO" desc="" />
        <Define name="SHADER_LOC_MAP_SPECULAR" type="UNKNOWN" value="SHADER_LOC_MAP_METALNESS" desc="" />
    </Defines>
    <Structs count="32">
        <Struct name="Vector2" fieldCount="2" desc="Vector2, 2 components">
            <Field type="float" name="x" desc="Vector x component" />
            <Field type="float" name="y" desc="Vector y component" />
        </Struct>
        <Struct name="Vector3" fieldCount="3" desc="Vector3, 3 components">
            <Field type="float" name="x" desc="Vector x component" />
            <Field type="float" name="y" desc="Vector y component" />
            <Field type="float" name="z" desc="Vector z component" />
        </Struct>
        <Struct name="Vector4" fieldCount="4" desc="Vector4, 4 components">
            <Field type="float" name="x" desc="Vector x component" />
            <Field type="float" name="y" desc="Vector y component" />
            <Field type="float" name="z" desc="Vector z component" />
            <Field type="float" name="w" desc="Vector w component" />
        </Struct>
        <Struct name="Matrix" fieldCount="16" desc="Matrix, 4x4 components, column major, OpenGL style, right-handed">
            <Field type="float" name="m0" desc="Matrix first row (4 components)" />
            <Field type="float" name="m4" desc="Matrix first row (4 components)" />
            <Field type="float" name="m8" desc="Matrix first row (4 components)" />
            <Field type="float" name="m12" desc="Matrix first row (4 components)" />
            <Field type="float" name="m1" desc="Matrix second row (4 components)" />
            <Field type="float" name="m5" desc="Matrix second row (4 components)" />
            <Field type="float" name="m9" desc="Matrix second row (4 components)" />
            <Field type="float" name="m13" desc="Matrix second row (4 components)" />
            <Field type="float" name="m2" desc="Matrix third row (4 components)" />
            <Field type="float" name="m6" desc="Matrix third row (4 components)" />
            <Field type="float" name="m10" desc="Matrix third row (4 components)" />
            <Field type="float" name="m14" desc="Matrix third row (4 components)" />
            <Field type="float" name="m3" desc="Matrix fourth row (4 components)" />
            <Field type="float" name="m7" desc="Matrix fourth row (4 components)" />
            <Field type="float" name="m11" desc="Matrix fourth row (4 components)" />
            <Field type="float" name="m15" desc="Matrix fourth row (4 components)" />
        </Struct>
        <Struct name="Color" fieldCount="4" desc="Color, 4 components, R8G8B8A8 (32bit)">
            <Field type="unsigned char" name="r" desc="Color red value" />
            <Field type="unsigned char" name="g" desc="Color green value" />
            <Field type="unsigned char" name="b" desc="Color blue value" />
            <Field type="unsigned char" name="a" desc="Color alpha value" />
        </Struct>
        <Struct name="Rectangle" fieldCount="4" desc="Rectangle, 4 components">
            <Field type="float" name="x" desc="Rectangle top-left corner position x" />
            <Field type="float" name="y" desc="Rectangle top-left corner position y" />
            <Field type="float" name="width" desc="Rectangle width" />
            <Field type="float" name="height" desc="Rectangle height" />
        </Struct>
        <Struct name="Image" fieldCount="5" desc="Image, pixel data stored in CPU memory (RAM)">
            <Field type="void *" name="data" desc="Image raw data" />
            <Field type="int" name="width" desc="Image base width" />
            <Field type="int" name="height" desc="Image base height" />
            <Field type="int" name="mipmaps" desc="Mipmap levels, 1 by default" />
            <Field type="int" name="format" desc="Data format (PixelFormat type)" />
        </Struct>
        <Struct name="Texture" fieldCount="5" desc="Texture, tex data stored in GPU memory (VRAM)">
            <Field type="unsigned int" name="id" desc="OpenGL texture id" />
            <Field type="int" name="width" desc="Texture base width" />
            <Field type="int" name="height" desc="Texture base height" />
            <Field type="int" name="mipmaps" desc="Mipmap levels, 1 by default" />
            <Field type="int" name="format" desc="Data format (PixelFormat type)" />
        </Struct>
        <Struct name="RenderTexture" fieldCount="3" desc="RenderTexture, fbo for texture rendering">
            <Field type="unsigned int" name="id" desc="OpenGL framebuffer object id" />
            <Field type="Texture" name="texture" desc="Color buffer attachment texture" />
            <Field type="Texture" name="depth" desc="Depth buffer attachment texture" />
        </Struct>
        <Struct name="NPatchInfo" fieldCount="6" desc="NPatchInfo, n-patch layout info">
            <Field type="Rectangle" name="source" desc="Texture source rectangle" />
            <Field type="int" name="left" desc="Left border offset" />
            <Field type="int" name="top" desc="Top border offset" />
            <Field type="int" name="right" desc="Right border offset" />
            <Field type="int" name="bottom" desc="Bottom border offset" />
            <Field type="int" name="layout" desc="Layout of the n-patch: 3x3, 1x3 or 3x1" />
        </Struct>
        <Struct name="GlyphInfo" fieldCount="5" desc="GlyphInfo, font characters glyphs info">
            <Field type="int" name="value" desc="Character value (Unicode)" />
            <Field type="int" name="offsetX" desc="Character offset X when drawing" />
            <Field type="int" name="offsetY" desc="Character offset Y when drawing" />
            <Field type="int" name="advanceX" desc="Character advance position X" />
            <Field type="Image" name="image" desc="Character image data" />
        </Struct>
        <Struct name="Font" fieldCount="6" desc="Font, font texture and GlyphInfo array data">
            <Field type="int" name="baseSize" desc="Base size (default chars height)" />
            <Field type="int" name="glyphCount" desc="Number of glyph characters" />
            <Field type="int" name="glyphPadding" desc="Padding around the glyph characters" />
            <Field type="Texture2D" name="texture" desc="Texture atlas containing the glyphs" />
            <Field type="Rectangle *" name="recs" desc="Rectangles in texture for the glyphs" />
            <Field type="GlyphInfo *" name="glyphs" desc="Glyphs info data" />
        </Struct>
        <Struct name="Camera3D" fieldCount="5" desc="Camera, defines position/orientation in 3d space">
            <Field type="Vector3" name="position" desc="Camera position" />
            <Field type="Vector3" name="target" desc="Camera target it looks-at" />
            <Field type="Vector3" name="up" desc="Camera up vector (rotation over its axis)" />
            <Field type="float" name="fovy" desc="Camera field-of-view aperture in Y (degrees) in perspective, used as near plane width in orthographic" />
            <Field type="int" name="projection" desc="Camera projection: CAMERA_PERSPECTIVE or CAMERA_ORTHOGRAPHIC" />
        </Struct>
        <Struct name="Camera2D" fieldCount="4" desc="Camera2D, defines position/orientation in 2d space">
            <Field type="Vector2" name="offset" desc="Camera offset (displacement from target)" />
            <Field type="Vector2" name="target" desc="Camera target (rotation and zoom origin)" />
            <Field type="float" name="rotation" desc="Camera rotation in degrees" />
            <Field type="float" name="zoom" desc="Camera zoom (scaling), should be 1.0f by default" />
        </Struct>
        <Struct name="Mesh" fieldCount="15" desc="Mesh, vertex data and vao/vbo">
            <Field type="int" name="vertexCount" desc="Number of vertices stored in arrays" />
            <Field type="int" name="triangleCount" desc="Number of triangles stored (indexed or not)" />
            <Field type="float *" name="vertices" desc="Vertex position (XYZ - 3 components per vertex) (shader-location = 0)" />
            <Field type="float *" name="texcoords" desc="Vertex texture coordinates (UV - 2 components per vertex) (shader-location = 1)" />
            <Field type="float *" name="texcoords2" desc="Vertex texture second coordinates (UV - 2 components per vertex) (shader-location = 5)" />
            <Field type="float *" name="normals" desc="Vertex normals (XYZ - 3 components per vertex) (shader-location = 2)" />
            <Field type="float *" name="tangents" desc="Vertex tangents (XYZW - 4 components per vertex) (shader-location = 4)" />
            <Field type="unsigned char *" name="colors" desc="Vertex colors (RGBA - 4 components per vertex) (shader-location = 3)" />
            <Field type="unsigned short *" name="indices" desc="Vertex indices (in case vertex data comes indexed)" />
            <Field type="float *" name="animVertices" desc="Animated vertex positions (after bones transformations)" />
            <Field type="float *" name="animNormals" desc="Animated normals (after bones transformations)" />
            <Field type="unsigned char *" name="boneIds" desc="Vertex bone ids, max 255 bone ids, up to 4 bones influence by vertex (skinning)" />
            <Field type="float *" name="boneWeights" desc="Vertex bone weight, up to 4 bones influence by vertex (skinning)" />
            <Field type="unsigned int" name="vaoId" desc="OpenGL Vertex Array Object id" />
            <Field type="unsigned int *" name="vboId" desc="OpenGL Vertex Buffer Objects id (default vertex data)" />
        </Struct>
        <Struct name="Shader" fieldCount="2" desc="Shader">
            <Field type="unsigned int" name="id" desc="Shader program id" />
            <Field type="int *" name="locs" desc="Shader locations array (RL_MAX_SHADER_LOCATIONS)" />
        </Struct>
        <Struct name="MaterialMap" fieldCount="3" desc="MaterialMap">
            <Field type="Texture2D" name="texture" desc="Material map texture" />
            <Field type="Color" name="color" desc="Material map color" />
            <Field type="float" name="value" desc="Material map value" />
        </Struct>
        <Struct name="Material" fieldCount="3" desc="Material, includes shader and maps">
            <Field type="Shader" name="shader" desc="Material shader" />
            <Field type="MaterialMap *" name="maps" desc="Material maps array (MAX_MATERIAL_MAPS)" />
            <Field type="float[4]" name="params" desc="Material generic parameters (if required)" />
        </Struct>
        <Struct name="Transform" fieldCount="3" desc="Transform, vertex transformation data">
            <Field type="Vector3" name="translation" desc="Translation" />
            <Field type="Quaternion" name="rotation" desc="Rotation" />
            <Field type="Vector3" name="scale" desc="Scale" />
        </Struct>
        <Struct name="BoneInfo" fieldCount="2" desc="Bone, skeletal animation bone">
            <Field type="char[32]" name="name" desc="Bone name" />
            <Field type="int" name="parent" desc="Bone parent" />
        </Struct>
        <Struct name="Model" fieldCount="9" desc="Model, meshes, materials and animation data">
            <Field type="Matrix" name="transform" desc="Local transform matrix" />
            <Field type="int" name="meshCount" desc="Number of meshes" />
            <Field type="int" name="materialCount" desc="Number of materials" />
            <Field type="Mesh *" name="meshes" desc="Meshes array" />
            <Field type="Material *" name="materials" desc="Materials array" />
            <Field type="int *" name="meshMaterial" desc="Mesh material number" />
            <Field type="int" name="boneCount" desc="Number of bones" />
            <Field type="BoneInfo *" name="bones" desc="Bones information (skeleton)" />
            <Field type="Transform *" name="bindPose" desc="Bones base transformation (pose)" />
        </Struct>
        <Struct name="ModelAnimation" fieldCount="4" desc="ModelAnimation">
            <Field type="int" name="boneCount" desc="Number of bones" />
            <Field type="int" name="frameCount" desc="Number of animation frames" />
            <Field type="BoneInfo *" name="bones" desc="Bones information (skeleton)" />
            <Field type="Transform **" name="framePoses" desc="Poses array by frame" />
        </Struct>
        <Struct name="Ray" fieldCount="2" desc="Ray, ray for raycasting">
            <Field type="Vector3" name="position" desc="Ray position (origin)" />
            <Field type="Vector3" name="direction" desc="Ray direction" />
        </Struct>
        <Struct name="RayCollision" fieldCount="4" desc="RayCollision, ray hit information">
            <Field type="bool" name="hit" desc="Did the ray hit something?" />
            <Field type="float" name="distance" desc="Distance to the nearest hit" />
            <Field type="Vector3" name="point" desc="Point of the nearest hit" />
            <Field type="Vector3" name="normal" desc="Surface normal of hit" />
        </Struct>
        <Struct name="BoundingBox" fieldCount="2" desc="BoundingBox">
            <Field type="Vector3" name="min" desc="Minimum vertex box-corner" />
            <Field type="Vector3" name="max" desc="Maximum vertex box-corner" />
        </Struct>
        <Struct name="Wave" fieldCount="5" desc="Wave, audio wave data">
            <Field type="unsigned int" name="frameCount" desc="Total number of frames (considering channels)" />
            <Field type="unsigned int" name="sampleRate" desc="Frequency (samples per second)" />
            <Field type="unsigned int" name="sampleSize" desc="Bit depth (bits per sample): 8, 16, 32 (24 not supported)" />
            <Field type="unsigned int" name="channels" desc="Number of channels (1-mono, 2-stereo, ...)" />
            <Field type="void *" name="data" desc="Buffer data pointer" />
        </Struct>
        <Struct name="AudioStream" fieldCount="5" desc="AudioStream, custom audio stream">
            <Field type="rAudioBuffer *" name="buffer" desc="Pointer to internal data used by the audio system" />
            <Field type="rAudioProcessor *" name="processor" desc="Pointer to internal data processor, useful for audio effects" />
            <Field type="unsigned int" name="sampleRate" desc="Frequency (samples per second)" />
            <Field type="unsigned int" name="sampleSize" desc="Bit depth (bits per sample): 8, 16, 32 (24 not supported)" />
            <Field type="unsigned int" name="channels" desc="Number of channels (1-mono, 2-stereo, ...)" />
        </Struct>
        <Struct name="Sound" fieldCount="2" desc="Sound">
            <Field type="AudioStream" name="stream" desc="Audio stream" />
            <Field type="unsigned int" name="frameCount" desc="Total number of frames (considering channels)" />
        </Struct>
        <Struct name="Music" fieldCount="5" desc="Music, audio stream, anything longer than ~10 seconds should be streamed">
            <Field type="AudioStream" name="stream" desc="Audio stream" />
            <Field type="unsigned int" name="frameCount" desc="Total number of frames (considering channels)" />
            <Field type="bool" name="looping" desc="Music looping enable" />
            <Field type="int" name="ctxType" desc="Type of music context (audio filetype)" />
            <Field type="void *" name="ctxData" desc="Audio context data, depends on type" />
        </Struct>
        <Struct name="VrDeviceInfo" fieldCount="10" desc="VrDeviceInfo, Head-Mounted-Display device parameters">
            <Field type="int" name="hResolution" desc="Horizontal resolution in pixels" />
            <Field type="int" name="vResolution" desc="Vertical resolution in pixels" />
            <Field type="float" name="hScreenSize" desc="Horizontal size in meters" />
            <Field type="float" name="vScreenSize" desc="Vertical size in meters" />
            <Field type="float" name="vScreenCenter" desc="Screen center in meters" />
            <Field type="float" name="eyeToScreenDistance" desc="Distance between eye and display in meters" />
            <Field type="float" name="lensSeparationDistance" desc="Lens separation distance in meters" />
            <Field type="float" name="interpupillaryDistance" desc="IPD (distance between pupils) in meters" />
            <Field type="float[4]" name="lensDistortionValues" desc="Lens distortion constant parameters" />
            <Field type="float[4]" name="chromaAbCorrection" desc="Chromatic aberration correction parameters" />
        </Struct>
        <Struct name="VrStereoConfig" fieldCount="8" desc="VrStereoConfig, VR stereo rendering configuration for simulator">
            <Field type="Matrix[2]" name="projection" desc="VR projection matrices (per eye)" />
            <Field type="Matrix[2]" name="viewOffset" desc="VR view offset matrices (per eye)" />
            <Field type="float[2]" name="leftLensCenter" desc="VR left lens center" />
            <Field type="float[2]" name="rightLensCenter" desc="VR right lens center" />
            <Field type="float[2]" name="leftScreenCenter" desc="VR left screen center" />
            <Field type="float[2]" name="rightScreenCenter" desc="VR right screen center" />
            <Field type="float[2]" name="scale" desc="VR distortion scale" />
            <Field type="float[2]" name="scaleIn" desc="VR distortion scale in" />
        </Struct>
        <Struct name="FilePathList" fieldCount="3" desc="File path list">
            <Field type="unsigned int" name="capacity" desc="Filepaths max entries" />
            <Field type="unsigned int" name="count" desc="Filepaths entries count" />
            <Field type="char **" name="paths" desc="Filepaths entries" />
        </Struct>
    </Structs>
    <Aliases count="5">
        <Alias type="Quaternion" name="Vector4" desc="Quaternion, 4 components (Vector4 alias)" />
        <Alias type="Texture2D" name="Texture" desc="Texture2D, same as Texture" />
        <Alias type="TextureCubemap" name="Texture" desc="TextureCubemap, same as Texture" />
        <Alias type="RenderTexture2D" name="RenderTexture" desc="RenderTexture2D, same as RenderTexture" />
        <Alias type="Camera" name="Camera3D" desc="Camera type fallback, defaults to Camera3D" />
    </Aliases>
    <Enums count="21">
        <Enum name="ConfigFlags" valueCount="15" desc="System/Window config flags">
            <Value name="FLAG_VSYNC_HINT" integer="64" desc="Set to try enabling V-Sync on GPU" />
            <Value name="FLAG_FULLSCREEN_MODE" integer="2" desc="Set to run program in fullscreen" />
            <Value name="FLAG_WINDOW_RESIZABLE" integer="4" desc="Set to allow resizable window" />
            <Value name="FLAG_WINDOW_UNDECORATED" integer="8" desc="Set to disable window decoration (frame and buttons)" />
            <Value name="FLAG_WINDOW_HIDDEN" integer="128" desc="Set to hide window" />
            <Value name="FLAG_WINDOW_MINIMIZED" integer="512" desc="Set to minimize window (iconify)" />
            <Value name="FLAG_WINDOW_MAXIMIZED" integer="1024" desc="Set to maximize window (expanded to monitor)" />
            <Value name="FLAG_WINDOW_UNFOCUSED" integer="2048" desc="Set to window non focused" />
            <Value name="FLAG_WINDOW_TOPMOST" integer="4096" desc="Set to window always on top" />
            <Value name="FLAG_WINDOW_ALWAYS_RUN" integer="256" desc="Set to allow windows running while minimized" />
            <Value name="FLAG_WINDOW_TRANSPARENT" integer="16" desc="Set to allow transparent framebuffer" />
            <Value name="FLAG_WINDOW_HIGHDPI" integer="8192" desc="Set to support HighDPI" />
            <Value name="FLAG_WINDOW_MOUSE_PASSTHROUGH" integer="16384" desc="Set to support mouse passthrough, only supported when FLAG_WINDOW_UNDECORATED" />
            <Value name="FLAG_MSAA_4X_HINT" integer="32" desc="Set to try enabling MSAA 4X" />
            <Value name="FLAG_INTERLACED_HINT" integer="65536" desc="Set to try enabling interlaced video format (for V3D)" />
        </Enum>
        <Enum name="TraceLogLevel" valueCount="8" desc="Trace log level">
            <Value name="LOG_ALL" integer="0" desc="Display all logs" />
            <Value name="LOG_TRACE" integer="1" desc="Trace logging, intended for internal use only" />
            <Value name="LOG_DEBUG" integer="2" desc="Debug logging, used for internal debugging, it should be disabled on release builds" />
            <Value name="LOG_INFO" integer="3" desc="Info logging, used for program execution info" />
            <Value name="LOG_WARNING" integer="4" desc="Warning logging, used on recoverable failures" />
            <Value name="LOG_ERROR" integer="5" desc="Error logging, used on unrecoverable failures" />
            <Value name="LOG_FATAL" integer="6" desc="Fatal logging, used to abort program: exit(EXIT_FAILURE)" />
            <Value name="LOG_NONE" integer="7" desc="Disable logging" />
        </Enum>
        <Enum name="KeyboardKey" valueCount="110" desc="Keyboard keys (US keyboard layout)">
            <Value name="KEY_NULL" integer="0" desc="Key: NULL, used for no key pressed" />
            <Value name="KEY_APOSTROPHE" integer="39" desc="Key: '" />
            <Value name="KEY_COMMA" integer="44" desc="Key: ," />
            <Value name="KEY_MINUS" integer="45" desc="Key: -" />
            <Value name="KEY_PERIOD" integer="46" desc="Key: ." />
            <Value name="KEY_SLASH" integer="47" desc="Key: /" />
            <Value name="KEY_ZERO" integer="48" desc="Key: 0" />
            <Value name="KEY_ONE" integer="49" desc="Key: 1" />
            <Value name="KEY_TWO" integer="50" desc="Key: 2" />
            <Value name="KEY_THREE" integer="51" desc="Key: 3" />
            <Value name="KEY_FOUR" integer="52" desc="Key: 4" />
            <Value name="KEY_FIVE" integer="53" desc="Key: 5" />
            <Value name="KEY_SIX" integer="54" desc="Key: 6" />
            <Value name="KEY_SEVEN" integer="55" desc="Key: 7" />
            <Value name="KEY_EIGHT" integer="56" desc="Key: 8" />
            <Value name="KEY_NINE" integer="57" desc="Key: 9" />
            <Value name="KEY_SEMICOLON" integer="59" desc="Key: ;" />
            <Value name="KEY_EQUAL" integer="61" desc="Key: =" />
            <Value name="KEY_A" integer="65" desc="Key: A | a" />
            <Value name="KEY_B" integer="66" desc="Key: B | b" />
            <Value name="KEY_C" integer="67" desc="Key: C | c" />
            <Value name="KEY_D" integer="68" desc="Key: D | d" />
            <Value name="KEY_E" integer="69" desc="Key: E | e" />
            <Value name="KEY_F" integer="70" desc="Key: F | f" />
            <Value name="KEY_G" integer="71" desc="Key: G | g" />
            <Value name="KEY_H" integer="72" desc="Key: H | h" />
            <Value name="KEY_I" integer="73" desc="Key: I | i" />
            <Value name="KEY_J" integer="74" desc="Key: J | j" />
            <Value name="KEY_K" integer="75" desc="Key: K | k" />
            <Value name="KEY_L" integer="76" desc="Key: L | l" />
            <Value name="KEY_M" integer="77" desc="Key: M | m" />
            <Value name="KEY_N" integer="78" desc="Key: N | n" />
            <Value name="KEY_O" integer="79" desc="Key: O | o" />
            <Value name="KEY_P" integer="80" desc="Key: P | p" />
            <Value name="KEY_Q" integer="81" desc="Key: Q | q" />
            <Value name="KEY_R" integer="82" desc="Key: R | r" />
            <Value name="KEY_S" integer="83" desc="Key: S | s" />
            <Value name="KEY_T" integer="84" desc="Key: T | t" />
            <Value name="KEY_U" integer="85" desc="Key: U | u" />
            <Value name="KEY_V" integer="86" desc="Key: V | v" />
            <Value name="KEY_W" integer="87" desc="Key: W | w" />
            <Value name="KEY_X" integer="88" desc="Key: X | x" />
            <Value name="KEY_Y" integer="89" desc="Key: Y | y" />
            <Value name="KEY_Z" integer="90" desc="Key: Z | z" />
            <Value name="KEY_LEFT_BRACKET" integer="91" desc="Key: [" />
            <Value name="KEY_BACKSLASH" integer="92" desc="Key: '\'" />
            <Value name="KEY_RIGHT_BRACKET" integer="93" desc="Key: ]" />
            <Value name="KEY_GRAVE" integer="96" desc="Key: `" />
            <Value name="KEY_SPACE" integer="32" desc="Key: Space" />
            <Value name="KEY_ESCAPE" integer="256" desc="Key: Esc" />
            <Value name="KEY_ENTER" integer="257" desc="Key: Enter" />
            <Value name="KEY_TAB" integer="258" desc="Key: Tab" />
            <Value name="KEY_BACKSPACE" integer="259" desc="Key: Backspace" />
            <Value name="KEY_INSERT" integer="260" desc="Key: Ins" />
            <Value name="KEY_DELETE" integer="261" desc="Key: Del" />
            <Value name="KEY_RIGHT" integer="262" desc="Key: Cursor right" />
            <Value name="KEY_LEFT" integer="263" desc="Key: Cursor left" />
            <Value name="KEY_DOWN" integer="264" desc="Key: Cursor down" />
            <Value name="KEY_UP" integer="265" desc="Key: Cursor up" />
            <Value name="KEY_PAGE_UP" integer="266" desc="Key: Page up" />
            <Value name="KEY_PAGE_DOWN" integer="267" desc="Key: Page down" />
            <Value name="KEY_HOME" integer="268" desc="Key: Home" />
            <Value name="KEY_END" integer="269" desc="Key: End" />
            <Value name="KEY_CAPS_LOCK" integer="280" desc="Key: Caps lock" />
            <Value name="KEY_SCROLL_LOCK" integer="281" desc="Key: Scroll down" />
            <Value name="KEY_NUM_LOCK" integer="282" desc="Key: Num lock" />
            <Value name="KEY_PRINT_SCREEN" integer="283" desc="Key: Print screen" />
            <Value name="KEY_PAUSE" integer="284" desc="Key: Pause" />
            <Value name="KEY_F1" integer="290" desc="Key: F1" />
            <Value name="KEY_F2" integer="291" desc="Key: F2" />
            <Value name="KEY_F3" integer="292" desc="Key: F3" />
            <Value name="KEY_F4" integer="293" desc="Key: F4" />
            <Value name="KEY_F5" integer="294" desc="Key: F5" />
            <Value name="KEY_F6" integer="295" desc="Key: F6" />
            <Value name="KEY_F7" integer="296" desc="Key: F7" />
            <Value name="KEY_F8" integer="297" desc="Key: F8" />
            <Value name="KEY_F9" integer="298" desc="Key: F9" />
            <Value name="KEY_F10" integer="299" desc="Key: F10" />
            <Value name="KEY_F11" integer="300" desc="Key: F11" />
            <Value name="KEY_F12" integer="301" desc="Key: F12" />
            <Value name="KEY_LEFT_SHIFT" integer="340" desc="Key: Shift left" />
            <Value name="KEY_LEFT_CONTROL" integer="341" desc="Key: Control left" />
            <Value name="KEY_LEFT_ALT" integer="342" desc="Key: Alt left" />
            <Value name="KEY_LEFT_SUPER" integer="343" desc="Key: Super left" />
            <Value name="KEY_RIGHT_SHIFT" integer="344" desc="Key: Shift right" />
            <Value name="KEY_RIGHT_CONTROL" integer="345" desc="Key: Control right" />
            <Value name="KEY_RIGHT_ALT" integer="346" desc="Key: Alt right" />
            <Value name="KEY_RIGHT_SUPER" integer="347" desc="Key: Super right" />
            <Value name="KEY_KB_MENU" integer="348" desc="Key: KB menu" />
            <Value name="KEY_KP_0" integer="320" desc="Key: Keypad 0" />
            <Value name="KEY_KP_1" integer="321" desc="Key: Keypad 1" />
            <Value name="KEY_KP_2" integer="322" desc="Key: Keypad 2" />
            <Value name="KEY_KP_3" integer="323" desc="Key: Keypad 3" />
            <Value name="KEY_KP_4" integer="324" desc="Key: Keypad 4" />
            <Value name="KEY_KP_5" integer="325" desc="Key: Keypad 5" />
            <Value name="KEY_KP_6" integer="326" desc="Key: Keypad 6" />
            <Value name="KEY_KP_7" integer="327" desc="Key: Keypad 7" />
            <Value name="KEY_KP_8" integer="328" desc="Key: Keypad 8" />
            <Value name="KEY_KP_9" integer="329" desc="Key: Keypad 9" />
            <Value name="KEY_KP_DECIMAL" integer="330" desc="Key: Keypad ." />
            <Value name="KEY_KP_DIVIDE" integer="331" desc="Key: Keypad /" />
            <Value name="KEY_KP_MULTIPLY" integer="332" desc="Key: Keypad *" />
            <Value name="KEY_KP_SUBTRACT" integer="333" desc="Key: Keypad -" />
            <Value name="KEY_KP_ADD" integer="334" desc="Key: Keypad +" />
            <Value name="KEY_KP_ENTER" integer="335" desc="Key: Keypad Enter" />
            <Value name="KEY_KP_EQUAL" integer="336" desc="Key: Keypad =" />
            <Value name="KEY_BACK" integer="4" desc="Key: Android back button" />
            <Value name="KEY_MENU" integer="82" desc="Key: Android menu button" />
            <Value name="KEY_VOLUME_UP" integer="24" desc="Key: Android volume up button" />
            <Value name="KEY_VOLUME_DOWN" integer="25" desc="Key: Android volume down button" />
        </Enum>
        <Enum name="MouseButton" valueCount="7" desc="Mouse buttons">
            <Value name="MOUSE_BUTTON_LEFT" integer="0" desc="Mouse button left" />
            <Value name="MOUSE_BUTTON_RIGHT" integer="1" desc="Mouse button right" />
            <Value name="MOUSE_BUTTON_MIDDLE" integer="2" desc="Mouse button middle (pressed wheel)" />
            <Value name="MOUSE_BUTTON_SIDE" integer="3" desc="Mouse button side (advanced mouse device)" />
            <Value name="MOUSE_BUTTON_EXTRA" integer="4" desc="Mouse button extra (advanced mouse device)" />
            <Value name="MOUSE_BUTTON_FORWARD" integer="5" desc="Mouse button forward (advanced mouse device)" />
            <Value name="MOUSE_BUTTON_BACK" integer="6" desc="Mouse button back (advanced mouse device)" />
        </Enum>
        <Enum name="MouseCursor" valueCount="11" desc="Mouse cursor">
            <Value name="MOUSE_CURSOR_DEFAULT" integer="0" desc="Default pointer shape" />
            <Value name="MOUSE_CURSOR_ARROW" integer="1" desc="Arrow shape" />
            <Value name="MOUSE_CURSOR_IBEAM" integer="2" desc="Text writing cursor shape" />
            <Value name="MOUSE_CURSOR_CROSSHAIR" integer="3" desc="Cross shape" />
            <Value name="MOUSE_CURSOR_POINTING_HAND" integer="4" desc="Pointing hand cursor" />
            <Value name="MOUSE_CURSOR_RESIZE_EW" integer="5" desc="Horizontal resize/move arrow shape" />
            <Value name="MOUSE_CURSOR_RESIZE_NS" integer="6" desc="Vertical resize/move arrow shape" />
            <Value name="MOUSE_CURSOR_RESIZE_NWSE" integer="7" desc="Top-left to bottom-right diagonal resize/move arrow shape" />
            <Value name="MOUSE_CURSOR_RESIZE_NESW" integer="8" desc="The top-right to bottom-left diagonal resize/move arrow shape" />
            <Value name="MOUSE_CURSOR_RESIZE_ALL" integer="9" desc="The omnidirectional resize/move cursor shape" />
            <Value name="MOUSE_CURSOR_NOT_ALLOWED" integer="10" desc="The operation-not-allowed shape" />
        </Enum>
        <Enum name="GamepadButton" valueCount="18" desc="Gamepad buttons">
            <Value name="GAMEPAD_BUTTON_UNKNOWN" integer="0" desc="Unknown button, just for error checking" />
            <Value name="GAMEPAD_BUTTON_LEFT_FACE_UP" integer="1" desc="Gamepad left DPAD up button" />
            <Value name="GAMEPAD_BUTTON_LEFT_FACE_RIGHT" integer="2" desc="Gamepad left DPAD right button" />
            <Value name="GAMEPAD_BUTTON_LEFT_FACE_DOWN" integer="3" desc="Gamepad left DPAD down button" />
            <Value name="GAMEPAD_BUTTON_LEFT_FACE_LEFT" integer="4" desc="Gamepad left DPAD left button" />
            <Value name="GAMEPAD_BUTTON_RIGHT_FACE_UP" integer="5" desc="Gamepad right button up (i.e. PS3: Triangle, Xbox: Y)" />
            <Value name="GAMEPAD_BUTTON_RIGHT_FACE_RIGHT" integer="6" desc="Gamepad right button right (i.e. PS3: Square, Xbox: X)" />
            <Value name="GAMEPAD_BUTTON_RIGHT_FACE_DOWN" integer="7" desc="Gamepad right button down (i.e. PS3: Cross, Xbox: A)" />
            <Value name="GAMEPAD_BUTTON_RIGHT_FACE_LEFT" integer="8" desc="Gamepad right button left (i.e. PS3: Circle, Xbox: B)" />
            <Value name="GAMEPAD_BUTTON_LEFT_TRIGGER_1" integer="9" desc="Gamepad top/back trigger left (first), it could be a trailing button" />
            <Value name="GAMEPAD_BUTTON_LEFT_TRIGGER_2" integer="10" desc="Gamepad top/back trigger left (second), it could be a trailing button" />
            <Value name="GAMEPAD_BUTTON_RIGHT_TRIGGER_1" integer="11" desc="Gamepad top/back trigger right (one), it could be a trailing button" />
            <Value name="GAMEPAD_BUTTON_RIGHT_TRIGGER_2" integer="12" desc="Gamepad top/back trigger right (second), it could be a trailing button" />
            <Value name="GAMEPAD_BUTTON_MIDDLE_LEFT" integer="13" desc="Gamepad center buttons, left one (i.e. PS3: Select)" />
            <Value name="GAMEPAD_BUTTON_MIDDLE" integer="14" desc="Gamepad center buttons, middle one (i.e. PS3: PS, Xbox: XBOX)" />
            <Value name="GAMEPAD_BUTTON_MIDDLE_RIGHT" integer="15" desc="Gamepad center buttons, right one (i.e. PS3: Start)" />
            <Value name="GAMEPAD_BUTTON_LEFT_THUMB" integer="16" desc="Gamepad joystick pressed button left" />
            <Value name="GAMEPAD_BUTTON_RIGHT_THUMB" integer="17" desc="Gamepad joystick pressed button right" />
        </Enum>
        <Enum name="GamepadAxis" valueCount="6" desc="Gamepad axis">
            <Value name="GAMEPAD_AXIS_LEFT_X" integer="0" desc="Gamepad left stick X axis" />
            <Value name="GAMEPAD_AXIS_LEFT_Y" integer="1" desc="Gamepad left stick Y axis" />
            <Value name="GAMEPAD_AXIS_RIGHT_X" integer="2" desc="Gamepad right stick X axis" />
            <Value name="GAMEPAD_AXIS_RIGHT_Y" integer="3" desc="Gamepad right stick Y axis" />
            <Value name="GAMEPAD_AXIS_LEFT_TRIGGER" integer="4" desc="Gamepad back trigger left, pressure level: [1..-1]" />
            <Value name="GAMEPAD_AXIS_RIGHT_TRIGGER" integer="5" desc="Gamepad back trigger right, pressure level: [1..-1]" />
        </Enum>
        <Enum name="MaterialMapIndex" valueCount="11" desc="Material map index">
            <Value name="MATERIAL_MAP_ALBEDO" integer="0" desc="Albedo material (same as: MATERIAL_MAP_DIFFUSE)" />
            <Value name="MATERIAL_MAP_METALNESS" integer="1" desc="Metalness material (same as: MATERIAL_MAP_SPECULAR)" />
            <Value name="MATERIAL_MAP_NORMAL" integer="2" desc="Normal material" />
            <Value name="MATERIAL_MAP_ROUGHNESS" integer="3" desc="Roughness material" />
            <Value name="MATERIAL_MAP_OCCLUSION" integer="4" desc="Ambient occlusion material" />
            <Value name="MATERIAL_MAP_EMISSION" integer="5" desc="Emission material" />
            <Value name="MATERIAL_MAP_HEIGHT" integer="6" desc="Heightmap material" />
            <Value name="MATERIAL_MAP_CUBEMAP" integer="7" desc="Cubemap material (NOTE: Uses GL_TEXTURE_CUBE_MAP)" />
            <Value name="MATERIAL_MAP_IRRADIANCE" integer="8" desc="Irradiance material (NOTE: Uses GL_TEXTURE_CUBE_MAP)" />
            <Value name="MATERIAL_MAP_PREFILTER" integer="9" desc="Prefilter material (NOTE: Uses GL_TEXTURE_CUBE_MAP)" />
            <Value name="MATERIAL_MAP_BRDF" integer="10" desc="Brdf material" />
        </Enum>
        <Enum name="ShaderLocationIndex" valueCount="26" desc="Shader location index">
            <Value name="SHADER_LOC_VERTEX_POSITION" integer="0" desc="Shader location: vertex attribute: position" />
            <Value name="SHADER_LOC_VERTEX_TEXCOORD01" integer="1" desc="Shader location: vertex attribute: texcoord01" />
            <Value name="SHADER_LOC_VERTEX_TEXCOORD02" integer="2" desc="Shader location: vertex attribute: texcoord02" />
            <Value name="SHADER_LOC_VERTEX_NORMAL" integer="3" desc="Shader location: vertex attribute: normal" />
            <Value name="SHADER_LOC_VERTEX_TANGENT" integer="4" desc="Shader location: vertex attribute: tangent" />
            <Value name="SHADER_LOC_VERTEX_COLOR" integer="5" desc="Shader location: vertex attribute: color" />
            <Value name="SHADER_LOC_MATRIX_MVP" integer="6" desc="Shader location: matrix uniform: model-view-projection" />
            <Value name="SHADER_LOC_MATRIX_VIEW" integer="7" desc="Shader location: matrix uniform: view (camera transform)" />
            <Value name="SHADER_LOC_MATRIX_PROJECTION" integer="8" desc="Shader location: matrix uniform: projection" />
            <Value name="SHADER_LOC_MATRIX_MODEL" integer="9" desc="Shader location: matrix uniform: model (transform)" />
            <Value name="SHADER_LOC_MATRIX_NORMAL" integer="10" desc="Shader location: matrix uniform: normal" />
            <Value name="SHADER_LOC_VECTOR_VIEW" integer="11" desc="Shader location: vector uniform: view" />
            <Value name="SHADER_LOC_COLOR_DIFFUSE" integer="12" desc="Shader location: vector uniform: diffuse color" />
            <Value name="SHADER_LOC_COLOR_SPECULAR" integer="13" desc="Shader location: vector uniform: specular color" />
            <Value name="SHADER_LOC_COLOR_AMBIENT" integer="14" desc="Shader location: vector uniform: ambient color" />
            <Value name="SHADER_LOC_MAP_ALBEDO" integer="15" desc="Shader location: sampler2d texture: albedo (same as: SHADER_LOC_MAP_DIFFUSE)" />
            <Value name="SHADER_LOC_MAP_METALNESS" integer="16" desc="Shader location: sampler2d texture: metalness (same as: SHADER_LOC_MAP_SPECULAR)" />
            <Value name="SHADER_LOC_MAP_NORMAL" integer="17" desc="Shader location: sampler2d texture: normal" />
            <Value name="SHADER_LOC_MAP_ROUGHNESS" integer="18" desc="Shader location: sampler2d texture: roughness" />
            <Value name="SHADER_LOC_MAP_OCCLUSION" integer="19" desc="Shader location: sampler2d texture: occlusion" />
            <Value name="SHADER_LOC_MAP_EMISSION" integer="20" desc="Shader location: sampler2d texture: emission" />
            <Value name="SHADER_LOC_MAP_HEIGHT" integer="21" desc="Shader location: sampler2d texture: height" />
            <Value name="SHADER_LOC_MAP_CUBEMAP" integer="22" desc="Shader location: samplerCube texture: cubemap" />
            <Value name="SHADER_LOC_MAP_IRRADIANCE" integer="23" desc="Shader location: samplerCube texture: irradiance" />
            <Value name="SHADER_LOC_MAP_PREFILTER" integer="24" desc="Shader location: samplerCube texture: prefilter" />
            <Value name="SHADER_LOC_MAP_BRDF" integer="25" desc="Shader location: sampler2d texture: brdf" />
        </Enum>
        <Enum name="ShaderUniformDataType" valueCount="9" desc="Shader uniform data type">
            <Value name="SHADER_UNIFORM_FLOAT" integer="0" desc="Shader uniform type: float" />
            <Value name="SHADER_UNIFORM_VEC2" integer="1" desc="Shader uniform type: vec2 (2 float)" />
            <Value name="SHADER_UNIFORM_VEC3" integer="2" desc="Shader uniform type: vec3 (3 float)" />
            <Value name="SHADER_UNIFORM_VEC4" integer="3" desc="Shader uniform type: vec4 (4 float)" />
            <Value name="SHADER_UNIFORM_INT" integer="4" desc="Shader uniform type: int" />
            <Value name="SHADER_UNIFORM_IVEC2" integer="5" desc="Shader uniform type: ivec2 (2 int)" />
            <Value name="SHADER_UNIFORM_IVEC3" integer="6" desc="Shader uniform type: ivec3 (3 int)" />
            <Value name="SHADER_UNIFORM_IVEC4" integer="7" desc="Shader uniform type: ivec4 (4 int)" />
            <Value name="SHADER_UNIFORM_SAMPLER2D" integer="8" desc="Shader uniform type: sampler2d" />
        </Enum>
        <Enum name="ShaderAttributeDataType" valueCount="4" desc="Shader attribute data types">
            <Value name="SHADER_ATTRIB_FLOAT" integer="0" desc="Shader attribute type: float" />
            <Value name="SHADER_ATTRIB_VEC2" integer="1" desc="Shader attribute type: vec2 (2 float)" />
            <Value name="SHADER_ATTRIB_VEC3" integer="2" desc="Shader attribute type: vec3 (3 float)" />
            <Value name="SHADER_ATTRIB_VEC4" integer="3" desc="Shader attribute type: vec4 (4 float)" />
        </Enum>
        <Enum name="PixelFormat" valueCount="21" desc="Pixel formats">
            <Value name="PIXELFORMAT_UNCOMPRESSED_GRAYSCALE" integer="1" desc="8 bit per pixel (no alpha)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_GRAY_ALPHA" integer="2" desc="8*2 bpp (2 channels)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R5G6B5" integer="3" desc="16 bpp" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R8G8B8" integer="4" desc="24 bpp" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R5G5B5A1" integer="5" desc="16 bpp (1 bit alpha)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R4G4B4A4" integer="6" desc="16 bpp (4 bit alpha)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R8G8B8A8" integer="7" desc="32 bpp" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R32" integer="8" desc="32 bpp (1 channel - float)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R32G32B32" integer="9" desc="32*3 bpp (3 channels - float)" />
            <Value name="PIXELFORMAT_UNCOMPRESSED_R32G32B32A32" integer="10" desc="32*4 bpp (4 channels - float)" />
            <Value name="PIXELFORMAT_COMPRESSED_DXT1_RGB" integer="11" desc="4 bpp (no alpha)" />
            <Value name="PIXELFORMAT_COMPRESSED_DXT1_RGBA" integer="12" desc="4 bpp (1 bit alpha)" />
            <Value name="PIXELFORMAT_COMPRESSED_DXT3_RGBA" integer="13" desc="8 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_DXT5_RGBA" integer="14" desc="8 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_ETC1_RGB" integer="15" desc="4 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_ETC2_RGB" integer="16" desc="4 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_ETC2_EAC_RGBA" integer="17" desc="8 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_PVRT_RGB" integer="18" desc="4 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_PVRT_RGBA" integer="19" desc="4 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_ASTC_4x4_RGBA" integer="20" desc="8 bpp" />
            <Value name="PIXELFORMAT_COMPRESSED_ASTC_8x8_RGBA" integer="21" desc="2 bpp" />
        </Enum>
        <Enum name="TextureFilter" valueCount="6" desc="Texture parameters: filter mode">
            <Value name="TEXTURE_FILTER_POINT" integer="0" desc="No filter, just pixel approximation" />
            <Value name="TEXTURE_FILTER_BILINEAR" integer="1" desc="Linear filtering" />
            <Value name="TEXTURE_FILTER_TRILINEAR" integer="2" desc="Trilinear filtering (linear with mipmaps)" />
            <Value name="TEXTURE_FILTER_ANISOTROPIC_4X" integer="3" desc="Anisotropic filtering 4x" />
            <Value name="TEXTURE_FILTER_ANISOTROPIC_8X" integer="4" desc="Anisotropic filtering 8x" />
            <Value name="TEXTURE_FILTER_ANISOTROPIC_16X" integer="5" desc="Anisotropic filtering 16x" />
        </Enum>
        <Enum name="TextureWrap" valueCount="4" desc="Texture parameters: wrap mode">
            <Value name="TEXTURE_WRAP_REPEAT" integer="0" desc="Repeats texture in tiled mode" />
            <Value name="TEXTURE_WRAP_CLAMP" integer="1" desc="Clamps texture to edge pixel in tiled mode" />
            <Value name="TEXTURE_WRAP_MIRROR_REPEAT" integer="2" desc="Mirrors and repeats the texture in tiled mode" />
            <Value name="TEXTURE_WRAP_MIRROR_CLAMP" integer="3" desc="Mirrors and clamps to border the texture in tiled mode" />
        </Enum>
        <Enum name="CubemapLayout" valueCount="6" desc="Cubemap layouts">
            <Value name="CUBEMAP_LAYOUT_AUTO_DETECT" integer="0" desc="Automatically detect layout type" />
            <Value name="CUBEMAP_LAYOUT_LINE_VERTICAL" integer="1" desc="Layout is defined by a vertical line with faces" />
            <Value name="CUBEMAP_LAYOUT_LINE_HORIZONTAL" integer="2" desc="Layout is defined by a horizontal line with faces" />
            <Value name="CUBEMAP_LAYOUT_CROSS_THREE_BY_FOUR" integer="3" desc="Layout is defined by a 3x4 cross with cubemap faces" />
            <Value name="CUBEMAP_LAYOUT_CROSS_FOUR_BY_THREE" integer="4" desc="Layout is defined by a 4x3 cross with cubemap faces" />
            <Value name="CUBEMAP_LAYOUT_PANORAMA" integer="5" desc="Layout is defined by a panorama image (equirrectangular map)" />
        </Enum>
        <Enum name="FontType" valueCount="3" desc="Font type, defines generation method">
            <Value name="FONT_DEFAULT" integer="0" desc="Default font generation, anti-aliased" />
            <Value name="FONT_BITMAP" integer="1" desc="Bitmap font generation, no anti-aliasing" />
            <Value name="FONT_SDF" integer="2" desc="SDF font generation, requires external shader" />
        </Enum>
        <Enum name="BlendMode" valueCount="8" desc="Color blending modes (pre-defined)">
            <Value name="BLEND_ALPHA" integer="0" desc="Blend textures considering alpha (default)" />
            <Value name="BLEND_ADDITIVE" integer="1" desc="Blend textures adding colors" />
            <Value name="BLEND_MULTIPLIED" integer="2" desc="Blend textures multiplying colors" />
            <Value name="BLEND_ADD_COLORS" integer="3" desc="Blend textures adding colors (alternative)" />
            <Value name="BLEND_SUBTRACT_COLORS" integer="4" desc="Blend textures subtracting colors (alternative)" />
            <Value name="BLEND_ALPHA_PREMULTIPLY" integer="5" desc="Blend premultiplied textures considering alpha" />
            <Value name="BLEND_CUSTOM" integer="6" desc="Blend textures using custom src/dst factors (use rlSetBlendFactors())" />
            <Value name="BLEND_CUSTOM_SEPARATE" integer="7" desc="Blend textures using custom rgb/alpha separate src/dst factors (use rlSetBlendFactorsSeparate())" />
        </Enum>
        <Enum name="Gesture" valueCount="11" desc="Gesture">
            <Value name="GESTURE_NONE" integer="0" desc="No gesture" />
            <Value name="GESTURE_TAP" integer="1" desc="Tap gesture" />
            <Value name="GESTURE_DOUBLETAP" integer="2" desc="Double tap gesture" />
            <Value name="GESTURE_HOLD" integer="4" desc="Hold gesture" />
            <Value name="GESTURE_DRAG" integer="8" desc="Drag gesture" />
            <Value name="GESTURE_SWIPE_RIGHT" integer="16" desc="Swipe right gesture" />
            <Value name="GESTURE_SWIPE_LEFT" integer="32" desc="Swipe left gesture" />
            <Value name="GESTURE_SWIPE_UP" integer="64" desc="Swipe up gesture" />
            <Value name="GESTURE_SWIPE_DOWN" integer="128" desc="Swipe down gesture" />
            <Value name="GESTURE_PINCH_IN" integer="256" desc="Pinch in gesture" />
            <Value name="GESTURE_PINCH_OUT" integer="512" desc="Pinch out gesture" />
        </Enum>
        <Enum name="CameraMode" valueCount="5" desc="Camera system modes">
            <Value name="CAMERA_CUSTOM" integer="0" desc="Custom camera" />
            <Value name="CAMERA_FREE" integer="1" desc="Free camera" />
            <Value name="CAMERA_ORBITAL" integer="2" desc="Orbital camera" />
            <Value name="CAMERA_FIRST_PERSON" integer="3" desc="First person camera" />
            <Value name="CAMERA_THIRD_PERSON" integer="4" desc="Third person camera" />
        </Enum>
        <Enum name="CameraProjection" valueCount="2" desc="Camera projection">
            <Value name="CAMERA_PERSPECTIVE" integer="0" desc="Perspective projection" />
            <Value name="CAMERA_ORTHOGRAPHIC" integer="1" desc="Orthographic projection" />
        </Enum>
        <Enum name="NPatchLayout" valueCount="3" desc="N-patch layout">
            <Value name="NPATCH_NINE_PATCH" integer="0" desc="Npatch layout: 3x3 tiles" />
            <Value name="NPATCH_THREE_PATCH_VERTICAL" integer="1" desc="Npatch layout: 1x3 tiles" />
            <Value name="NPATCH_THREE_PATCH_HORIZONTAL" integer="2" desc="Npatch layout: 3x1 tiles" />
        </Enum>
    </Enums>
    <Callbacks count="6">
        <Callback name="TraceLogCallback" retType="void" paramCount="3" desc="Logging: Redirect trace log messages">
            <Param type="int" name="logLevel" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="va_list" name="args" desc="" />
        </Callback>
        <Callback name="LoadFileDataCallback" retType="unsigned char *" paramCount="2" desc="FileIO: Load binary data">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="unsigned int *" name="bytesRead" desc="" />
        </Callback>
        <Callback name="SaveFileDataCallback" retType="bool" paramCount="3" desc="FileIO: Save binary data">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="void *" name="data" desc="" />
            <Param type="unsigned int" name="bytesToWrite" desc="" />
        </Callback>
        <Callback name="LoadFileTextCallback" retType="char *" paramCount="1" desc="FileIO: Load text data">
            <Param type="const char *" name="fileName" desc="" />
        </Callback>
        <Callback name="SaveFileTextCallback" retType="bool" paramCount="2" desc="FileIO: Save text data">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="char *" name="text" desc="" />
        </Callback>
        <Callback name="AudioCallback" retType="void" paramCount="2" desc="">
            <Param type="void *" name="bufferData" desc="" />
            <Param type="unsigned int" name="frames" desc="" />
        </Callback>
    </Callbacks>
    <Functions count="517">
        <Function name="InitWindow" retType="void" paramCount="3" desc="Initialize window and OpenGL context">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="const char *" name="title" desc="" />
        </Function>
        <Function name="WindowShouldClose" retType="bool" paramCount="0" desc="Check if KEY_ESCAPE pressed or Close icon pressed">
        </Function>
        <Function name="CloseWindow" retType="void" paramCount="0" desc="Close window and unload OpenGL context">
        </Function>
        <Function name="IsWindowReady" retType="bool" paramCount="0" desc="Check if window has been initialized successfully">
        </Function>
        <Function name="IsWindowFullscreen" retType="bool" paramCount="0" desc="Check if window is currently fullscreen">
        </Function>
        <Function name="IsWindowHidden" retType="bool" paramCount="0" desc="Check if window is currently hidden (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="IsWindowMinimized" retType="bool" paramCount="0" desc="Check if window is currently minimized (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="IsWindowMaximized" retType="bool" paramCount="0" desc="Check if window is currently maximized (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="IsWindowFocused" retType="bool" paramCount="0" desc="Check if window is currently focused (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="IsWindowResized" retType="bool" paramCount="0" desc="Check if window has been resized last frame">
        </Function>
        <Function name="IsWindowState" retType="bool" paramCount="1" desc="Check if one specific window flag is enabled">
            <Param type="unsigned int" name="flag" desc="" />
        </Function>
        <Function name="SetWindowState" retType="void" paramCount="1" desc="Set window configuration state using flags (only PLATFORM_DESKTOP)">
            <Param type="unsigned int" name="flags" desc="" />
        </Function>
        <Function name="ClearWindowState" retType="void" paramCount="1" desc="Clear window configuration state flags">
            <Param type="unsigned int" name="flags" desc="" />
        </Function>
        <Function name="ToggleFullscreen" retType="void" paramCount="0" desc="Toggle window state: fullscreen/windowed (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="MaximizeWindow" retType="void" paramCount="0" desc="Set window state: maximized, if resizable (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="MinimizeWindow" retType="void" paramCount="0" desc="Set window state: minimized, if resizable (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="RestoreWindow" retType="void" paramCount="0" desc="Set window state: not minimized/maximized (only PLATFORM_DESKTOP)">
        </Function>
        <Function name="SetWindowIcon" retType="void" paramCount="1" desc="Set icon for window (single image, RGBA 32bit, only PLATFORM_DESKTOP)">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="SetWindowIcons" retType="void" paramCount="2" desc="Set icon for window (multiple images, RGBA 32bit, only PLATFORM_DESKTOP)">
            <Param type="Image *" name="images" desc="" />
            <Param type="int" name="count" desc="" />
        </Function>
        <Function name="SetWindowTitle" retType="void" paramCount="1" desc="Set title for window (only PLATFORM_DESKTOP)">
            <Param type="const char *" name="title" desc="" />
        </Function>
        <Function name="SetWindowPosition" retType="void" paramCount="2" desc="Set window position on screen (only PLATFORM_DESKTOP)">
            <Param type="int" name="x" desc="" />
            <Param type="int" name="y" desc="" />
        </Function>
        <Function name="SetWindowMonitor" retType="void" paramCount="1" desc="Set monitor for the current window (fullscreen mode)">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="SetWindowMinSize" retType="void" paramCount="2" desc="Set window minimum dimensions (for FLAG_WINDOW_RESIZABLE)">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
        </Function>
        <Function name="SetWindowSize" retType="void" paramCount="2" desc="Set window dimensions">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
        </Function>
        <Function name="SetWindowOpacity" retType="void" paramCount="1" desc="Set window opacity [0.0f..1.0f] (only PLATFORM_DESKTOP)">
            <Param type="float" name="opacity" desc="" />
        </Function>
        <Function name="GetWindowHandle" retType="void *" paramCount="0" desc="Get native window handle">
        </Function>
        <Function name="GetScreenWidth" retType="int" paramCount="0" desc="Get current screen width">
        </Function>
        <Function name="GetScreenHeight" retType="int" paramCount="0" desc="Get current screen height">
        </Function>
        <Function name="GetRenderWidth" retType="int" paramCount="0" desc="Get current render width (it considers HiDPI)">
        </Function>
        <Function name="GetRenderHeight" retType="int" paramCount="0" desc="Get current render height (it considers HiDPI)">
        </Function>
        <Function name="GetMonitorCount" retType="int" paramCount="0" desc="Get number of connected monitors">
        </Function>
        <Function name="GetCurrentMonitor" retType="int" paramCount="0" desc="Get current connected monitor">
        </Function>
        <Function name="GetMonitorPosition" retType="Vector2" paramCount="1" desc="Get specified monitor position">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetMonitorWidth" retType="int" paramCount="1" desc="Get specified monitor width (current video mode used by monitor)">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetMonitorHeight" retType="int" paramCount="1" desc="Get specified monitor height (current video mode used by monitor)">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetMonitorPhysicalWidth" retType="int" paramCount="1" desc="Get specified monitor physical width in millimetres">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetMonitorPhysicalHeight" retType="int" paramCount="1" desc="Get specified monitor physical height in millimetres">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetMonitorRefreshRate" retType="int" paramCount="1" desc="Get specified monitor refresh rate">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="GetWindowPosition" retType="Vector2" paramCount="0" desc="Get window position XY on monitor">
        </Function>
        <Function name="GetWindowScaleDPI" retType="Vector2" paramCount="0" desc="Get window scale DPI factor">
        </Function>
        <Function name="GetMonitorName" retType="const char *" paramCount="1" desc="Get the human-readable, UTF-8 encoded name of the primary monitor">
            <Param type="int" name="monitor" desc="" />
        </Function>
        <Function name="SetClipboardText" retType="void" paramCount="1" desc="Set clipboard text content">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="GetClipboardText" retType="const char *" paramCount="0" desc="Get clipboard text content">
        </Function>
        <Function name="EnableEventWaiting" retType="void" paramCount="0" desc="Enable waiting for events on EndDrawing(), no automatic event polling">
        </Function>
        <Function name="DisableEventWaiting" retType="void" paramCount="0" desc="Disable waiting for events on EndDrawing(), automatic events polling">
        </Function>
        <Function name="SwapScreenBuffer" retType="void" paramCount="0" desc="Swap back buffer with front buffer (screen drawing)">
        </Function>
        <Function name="PollInputEvents" retType="void" paramCount="0" desc="Register all input events">
        </Function>
        <Function name="WaitTime" retType="void" paramCount="1" desc="Wait for some time (halt program execution)">
            <Param type="double" name="seconds" desc="" />
        </Function>
        <Function name="ShowCursor" retType="void" paramCount="0" desc="Shows cursor">
        </Function>
        <Function name="HideCursor" retType="void" paramCount="0" desc="Hides cursor">
        </Function>
        <Function name="IsCursorHidden" retType="bool" paramCount="0" desc="Check if cursor is not visible">
        </Function>
        <Function name="EnableCursor" retType="void" paramCount="0" desc="Enables cursor (unlock cursor)">
        </Function>
        <Function name="DisableCursor" retType="void" paramCount="0" desc="Disables cursor (lock cursor)">
        </Function>
        <Function name="IsCursorOnScreen" retType="bool" paramCount="0" desc="Check if cursor is on the screen">
        </Function>
        <Function name="ClearBackground" retType="void" paramCount="1" desc="Set background color (framebuffer clear color)">
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="BeginDrawing" retType="void" paramCount="0" desc="Setup canvas (framebuffer) to start drawing">
        </Function>
        <Function name="EndDrawing" retType="void" paramCount="0" desc="End canvas drawing and swap buffers (double buffering)">
        </Function>
        <Function name="BeginMode2D" retType="void" paramCount="1" desc="Begin 2D mode with custom camera (2D)">
            <Param type="Camera2D" name="camera" desc="" />
        </Function>
        <Function name="EndMode2D" retType="void" paramCount="0" desc="Ends 2D mode with custom camera">
        </Function>
        <Function name="BeginMode3D" retType="void" paramCount="1" desc="Begin 3D mode with custom camera (3D)">
            <Param type="Camera3D" name="camera" desc="" />
        </Function>
        <Function name="EndMode3D" retType="void" paramCount="0" desc="Ends 3D mode and returns to default 2D orthographic mode">
        </Function>
        <Function name="BeginTextureMode" retType="void" paramCount="1" desc="Begin drawing to render texture">
            <Param type="RenderTexture2D" name="target" desc="" />
        </Function>
        <Function name="EndTextureMode" retType="void" paramCount="0" desc="Ends drawing to render texture">
        </Function>
        <Function name="BeginShaderMode" retType="void" paramCount="1" desc="Begin custom shader drawing">
            <Param type="Shader" name="shader" desc="" />
        </Function>
        <Function name="EndShaderMode" retType="void" paramCount="0" desc="End custom shader drawing (use default shader)">
        </Function>
        <Function name="BeginBlendMode" retType="void" paramCount="1" desc="Begin blending mode (alpha, additive, multiplied, subtract, custom)">
            <Param type="int" name="mode" desc="" />
        </Function>
        <Function name="EndBlendMode" retType="void" paramCount="0" desc="End blending mode (reset to default: alpha blending)">
        </Function>
        <Function name="BeginScissorMode" retType="void" paramCount="4" desc="Begin scissor mode (define screen area for following drawing)">
            <Param type="int" name="x" desc="" />
            <Param type="int" name="y" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
        </Function>
        <Function name="EndScissorMode" retType="void" paramCount="0" desc="End scissor mode">
        </Function>
        <Function name="BeginVrStereoMode" retType="void" paramCount="1" desc="Begin stereo rendering (requires VR simulator)">
            <Param type="VrStereoConfig" name="config" desc="" />
        </Function>
        <Function name="EndVrStereoMode" retType="void" paramCount="0" desc="End stereo rendering (requires VR simulator)">
        </Function>
        <Function name="LoadVrStereoConfig" retType="VrStereoConfig" paramCount="1" desc="Load VR stereo config for VR simulator device parameters">
            <Param type="VrDeviceInfo" name="device" desc="" />
        </Function>
        <Function name="UnloadVrStereoConfig" retType="void" paramCount="1" desc="Unload VR stereo config">
            <Param type="VrStereoConfig" name="config" desc="" />
        </Function>
        <Function name="LoadShader" retType="Shader" paramCount="2" desc="Load shader from files and bind default locations">
            <Param type="const char *" name="vsFileName" desc="" />
            <Param type="const char *" name="fsFileName" desc="" />
        </Function>
        <Function name="LoadShaderFromMemory" retType="Shader" paramCount="2" desc="Load shader from code strings and bind default locations">
            <Param type="const char *" name="vsCode" desc="" />
            <Param type="const char *" name="fsCode" desc="" />
        </Function>
        <Function name="IsShaderReady" retType="bool" paramCount="1" desc="Check if a shader is ready">
            <Param type="Shader" name="shader" desc="" />
        </Function>
        <Function name="GetShaderLocation" retType="int" paramCount="2" desc="Get shader uniform location">
            <Param type="Shader" name="shader" desc="" />
            <Param type="const char *" name="uniformName" desc="" />
        </Function>
        <Function name="GetShaderLocationAttrib" retType="int" paramCount="2" desc="Get shader attribute location">
            <Param type="Shader" name="shader" desc="" />
            <Param type="const char *" name="attribName" desc="" />
        </Function>
        <Function name="SetShaderValue" retType="void" paramCount="4" desc="Set shader uniform value">
            <Param type="Shader" name="shader" desc="" />
            <Param type="int" name="locIndex" desc="" />
            <Param type="const void *" name="value" desc="" />
            <Param type="int" name="uniformType" desc="" />
        </Function>
        <Function name="SetShaderValueV" retType="void" paramCount="5" desc="Set shader uniform value vector">
            <Param type="Shader" name="shader" desc="" />
            <Param type="int" name="locIndex" desc="" />
            <Param type="const void *" name="value" desc="" />
            <Param type="int" name="uniformType" desc="" />
            <Param type="int" name="count" desc="" />
        </Function>
        <Function name="SetShaderValueMatrix" retType="void" paramCount="3" desc="Set shader uniform value (matrix 4x4)">
            <Param type="Shader" name="shader" desc="" />
            <Param type="int" name="locIndex" desc="" />
            <Param type="Matrix" name="mat" desc="" />
        </Function>
        <Function name="SetShaderValueTexture" retType="void" paramCount="3" desc="Set shader uniform value for texture (sampler2d)">
            <Param type="Shader" name="shader" desc="" />
            <Param type="int" name="locIndex" desc="" />
            <Param type="Texture2D" name="texture" desc="" />
        </Function>
        <Function name="UnloadShader" retType="void" paramCount="1" desc="Unload shader from GPU memory (VRAM)">
            <Param type="Shader" name="shader" desc="" />
        </Function>
        <Function name="GetMouseRay" retType="Ray" paramCount="2" desc="Get a ray trace from mouse position">
            <Param type="Vector2" name="mousePosition" desc="" />
            <Param type="Camera" name="camera" desc="" />
        </Function>
        <Function name="GetCameraMatrix" retType="Matrix" paramCount="1" desc="Get camera transform matrix (view matrix)">
            <Param type="Camera" name="camera" desc="" />
        </Function>
        <Function name="GetCameraMatrix2D" retType="Matrix" paramCount="1" desc="Get camera 2d transform matrix">
            <Param type="Camera2D" name="camera" desc="" />
        </Function>
        <Function name="GetWorldToScreen" retType="Vector2" paramCount="2" desc="Get the screen space position for a 3d world space position">
            <Param type="Vector3" name="position" desc="" />
            <Param type="Camera" name="camera" desc="" />
        </Function>
        <Function name="GetScreenToWorld2D" retType="Vector2" paramCount="2" desc="Get the world space position for a 2d camera screen space position">
            <Param type="Vector2" name="position" desc="" />
            <Param type="Camera2D" name="camera" desc="" />
        </Function>
        <Function name="GetWorldToScreenEx" retType="Vector2" paramCount="4" desc="Get size position for a 3d world space position">
            <Param type="Vector3" name="position" desc="" />
            <Param type="Camera" name="camera" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
        </Function>
        <Function name="GetWorldToScreen2D" retType="Vector2" paramCount="2" desc="Get the screen space position for a 2d camera world space position">
            <Param type="Vector2" name="position" desc="" />
            <Param type="Camera2D" name="camera" desc="" />
        </Function>
        <Function name="SetTargetFPS" retType="void" paramCount="1" desc="Set target FPS (maximum)">
            <Param type="int" name="fps" desc="" />
        </Function>
        <Function name="GetFPS" retType="int" paramCount="0" desc="Get current FPS">
        </Function>
        <Function name="GetFrameTime" retType="float" paramCount="0" desc="Get time in seconds for last frame drawn (delta time)">
        </Function>
        <Function name="GetTime" retType="double" paramCount="0" desc="Get elapsed time in seconds since InitWindow()">
        </Function>
        <Function name="GetRandomValue" retType="int" paramCount="2" desc="Get a random value between min and max (both included)">
            <Param type="int" name="min" desc="" />
            <Param type="int" name="max" desc="" />
        </Function>
        <Function name="SetRandomSeed" retType="void" paramCount="1" desc="Set the seed for the random number generator">
            <Param type="unsigned int" name="seed" desc="" />
        </Function>
        <Function name="TakeScreenshot" retType="void" paramCount="1" desc="Takes a screenshot of current screen (filename extension defines format)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="SetConfigFlags" retType="void" paramCount="1" desc="Setup init configuration flags (view FLAGS)">
            <Param type="unsigned int" name="flags" desc="" />
        </Function>
        <Function name="TraceLog" retType="void" paramCount="3" desc="Show trace log messages (LOG_DEBUG, LOG_INFO, LOG_WARNING, LOG_ERROR...)">
            <Param type="int" name="logLevel" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="..." name="args" desc="" />
        </Function>
        <Function name="SetTraceLogLevel" retType="void" paramCount="1" desc="Set the current threshold (minimum) log level">
            <Param type="int" name="logLevel" desc="" />
        </Function>
        <Function name="MemAlloc" retType="void *" paramCount="1" desc="Internal memory allocator">
            <Param type="unsigned int" name="size" desc="" />
        </Function>
        <Function name="MemRealloc" retType="void *" paramCount="2" desc="Internal memory reallocator">
            <Param type="void *" name="ptr" desc="" />
            <Param type="unsigned int" name="size" desc="" />
        </Function>
        <Function name="MemFree" retType="void" paramCount="1" desc="Internal memory free">
            <Param type="void *" name="ptr" desc="" />
        </Function>
        <Function name="OpenURL" retType="void" paramCount="1" desc="Open URL with default system browser (if available)">
            <Param type="const char *" name="url" desc="" />
        </Function>
        <Function name="SetTraceLogCallback" retType="void" paramCount="1" desc="Set custom trace log">
            <Param type="TraceLogCallback" name="callback" desc="" />
        </Function>
        <Function name="SetLoadFileDataCallback" retType="void" paramCount="1" desc="Set custom file binary data loader">
            <Param type="LoadFileDataCallback" name="callback" desc="" />
        </Function>
        <Function name="SetSaveFileDataCallback" retType="void" paramCount="1" desc="Set custom file binary data saver">
            <Param type="SaveFileDataCallback" name="callback" desc="" />
        </Function>
        <Function name="SetLoadFileTextCallback" retType="void" paramCount="1" desc="Set custom file text data loader">
            <Param type="LoadFileTextCallback" name="callback" desc="" />
        </Function>
        <Function name="SetSaveFileTextCallback" retType="void" paramCount="1" desc="Set custom file text data saver">
            <Param type="SaveFileTextCallback" name="callback" desc="" />
        </Function>
        <Function name="LoadFileData" retType="unsigned char *" paramCount="2" desc="Load file data as byte array (read)">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="unsigned int *" name="bytesRead" desc="" />
        </Function>
        <Function name="UnloadFileData" retType="void" paramCount="1" desc="Unload file data allocated by LoadFileData()">
            <Param type="unsigned char *" name="data" desc="" />
        </Function>
        <Function name="SaveFileData" retType="bool" paramCount="3" desc="Save data to file from byte array (write), returns true on success">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="void *" name="data" desc="" />
            <Param type="unsigned int" name="bytesToWrite" desc="" />
        </Function>
        <Function name="ExportDataAsCode" retType="bool" paramCount="3" desc="Export data to code (.h), returns true on success">
            <Param type="const unsigned char *" name="data" desc="" />
            <Param type="unsigned int" name="size" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadFileText" retType="char *" paramCount="1" desc="Load text data from file (read), returns a '\0' terminated string">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="UnloadFileText" retType="void" paramCount="1" desc="Unload file text data allocated by LoadFileText()">
            <Param type="char *" name="text" desc="" />
        </Function>
        <Function name="SaveFileText" retType="bool" paramCount="2" desc="Save text data to file (write), string must be '\0' terminated, returns true on success">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="char *" name="text" desc="" />
        </Function>
        <Function name="FileExists" retType="bool" paramCount="1" desc="Check if file exists">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="DirectoryExists" retType="bool" paramCount="1" desc="Check if a directory path exists">
            <Param type="const char *" name="dirPath" desc="" />
        </Function>
        <Function name="IsFileExtension" retType="bool" paramCount="2" desc="Check file extension (including point: .png, .wav)">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="const char *" name="ext" desc="" />
        </Function>
        <Function name="GetFileLength" retType="int" paramCount="1" desc="Get file length in bytes (NOTE: GetFileSize() conflicts with windows.h)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="GetFileExtension" retType="const char *" paramCount="1" desc="Get pointer to extension for a filename string (includes dot: '.png')">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="GetFileName" retType="const char *" paramCount="1" desc="Get pointer to filename for a path string">
            <Param type="const char *" name="filePath" desc="" />
        </Function>
        <Function name="GetFileNameWithoutExt" retType="const char *" paramCount="1" desc="Get filename string without extension (uses static string)">
            <Param type="const char *" name="filePath" desc="" />
        </Function>
        <Function name="GetDirectoryPath" retType="const char *" paramCount="1" desc="Get full path for a given fileName with path (uses static string)">
            <Param type="const char *" name="filePath" desc="" />
        </Function>
        <Function name="GetPrevDirectoryPath" retType="const char *" paramCount="1" desc="Get previous directory path for a given path (uses static string)">
            <Param type="const char *" name="dirPath" desc="" />
        </Function>
        <Function name="GetWorkingDirectory" retType="const char *" paramCount="0" desc="Get current working directory (uses static string)">
        </Function>
        <Function name="GetApplicationDirectory" retType="const char *" paramCount="0" desc="Get the directory if the running application (uses static string)">
        </Function>
        <Function name="ChangeDirectory" retType="bool" paramCount="1" desc="Change working directory, return true on success">
            <Param type="const char *" name="dir" desc="" />
        </Function>
        <Function name="IsPathFile" retType="bool" paramCount="1" desc="Check if a given path is a file or a directory">
            <Param type="const char *" name="path" desc="" />
        </Function>
        <Function name="LoadDirectoryFiles" retType="FilePathList" paramCount="1" desc="Load directory filepaths">
            <Param type="const char *" name="dirPath" desc="" />
        </Function>
        <Function name="LoadDirectoryFilesEx" retType="FilePathList" paramCount="3" desc="Load directory filepaths with extension filtering and recursive directory scan">
            <Param type="const char *" name="basePath" desc="" />
            <Param type="const char *" name="filter" desc="" />
            <Param type="bool" name="scanSubdirs" desc="" />
        </Function>
        <Function name="UnloadDirectoryFiles" retType="void" paramCount="1" desc="Unload filepaths">
            <Param type="FilePathList" name="files" desc="" />
        </Function>
        <Function name="IsFileDropped" retType="bool" paramCount="0" desc="Check if a file has been dropped into window">
        </Function>
        <Function name="LoadDroppedFiles" retType="FilePathList" paramCount="0" desc="Load dropped filepaths">
        </Function>
        <Function name="UnloadDroppedFiles" retType="void" paramCount="1" desc="Unload dropped filepaths">
            <Param type="FilePathList" name="files" desc="" />
        </Function>
        <Function name="GetFileModTime" retType="long" paramCount="1" desc="Get file modification time (last write time)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="CompressData" retType="unsigned char *" paramCount="3" desc="Compress data (DEFLATE algorithm), memory must be MemFree()">
            <Param type="const unsigned char *" name="data" desc="" />
            <Param type="int" name="dataSize" desc="" />
            <Param type="int *" name="compDataSize" desc="" />
        </Function>
        <Function name="DecompressData" retType="unsigned char *" paramCount="3" desc="Decompress data (DEFLATE algorithm), memory must be MemFree()">
            <Param type="const unsigned char *" name="compData" desc="" />
            <Param type="int" name="compDataSize" desc="" />
            <Param type="int *" name="dataSize" desc="" />
        </Function>
        <Function name="EncodeDataBase64" retType="char *" paramCount="3" desc="Encode data to Base64 string, memory must be MemFree()">
            <Param type="const unsigned char *" name="data" desc="" />
            <Param type="int" name="dataSize" desc="" />
            <Param type="int *" name="outputSize" desc="" />
        </Function>
        <Function name="DecodeDataBase64" retType="unsigned char *" paramCount="2" desc="Decode Base64 string data, memory must be MemFree()">
            <Param type="const unsigned char *" name="data" desc="" />
            <Param type="int *" name="outputSize" desc="" />
        </Function>
        <Function name="IsKeyPressed" retType="bool" paramCount="1" desc="Check if a key has been pressed once">
            <Param type="int" name="key" desc="" />
        </Function>
        <Function name="IsKeyDown" retType="bool" paramCount="1" desc="Check if a key is being pressed">
            <Param type="int" name="key" desc="" />
        </Function>
        <Function name="IsKeyReleased" retType="bool" paramCount="1" desc="Check if a key has been released once">
            <Param type="int" name="key" desc="" />
        </Function>
        <Function name="IsKeyUp" retType="bool" paramCount="1" desc="Check if a key is NOT being pressed">
            <Param type="int" name="key" desc="" />
        </Function>
        <Function name="SetExitKey" retType="void" paramCount="1" desc="Set a custom key to exit program (default is ESC)">
            <Param type="int" name="key" desc="" />
        </Function>
        <Function name="GetKeyPressed" retType="int" paramCount="0" desc="Get key pressed (keycode), call it multiple times for keys queued, returns 0 when the queue is empty">
        </Function>
        <Function name="GetCharPressed" retType="int" paramCount="0" desc="Get char pressed (unicode), call it multiple times for chars queued, returns 0 when the queue is empty">
        </Function>
        <Function name="IsGamepadAvailable" retType="bool" paramCount="1" desc="Check if a gamepad is available">
            <Param type="int" name="gamepad" desc="" />
        </Function>
        <Function name="GetGamepadName" retType="const char *" paramCount="1" desc="Get gamepad internal name id">
            <Param type="int" name="gamepad" desc="" />
        </Function>
        <Function name="IsGamepadButtonPressed" retType="bool" paramCount="2" desc="Check if a gamepad button has been pressed once">
            <Param type="int" name="gamepad" desc="" />
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsGamepadButtonDown" retType="bool" paramCount="2" desc="Check if a gamepad button is being pressed">
            <Param type="int" name="gamepad" desc="" />
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsGamepadButtonReleased" retType="bool" paramCount="2" desc="Check if a gamepad button has been released once">
            <Param type="int" name="gamepad" desc="" />
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsGamepadButtonUp" retType="bool" paramCount="2" desc="Check if a gamepad button is NOT being pressed">
            <Param type="int" name="gamepad" desc="" />
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="GetGamepadButtonPressed" retType="int" paramCount="0" desc="Get the last gamepad button pressed">
        </Function>
        <Function name="GetGamepadAxisCount" retType="int" paramCount="1" desc="Get gamepad axis count for a gamepad">
            <Param type="int" name="gamepad" desc="" />
        </Function>
        <Function name="GetGamepadAxisMovement" retType="float" paramCount="2" desc="Get axis movement value for a gamepad axis">
            <Param type="int" name="gamepad" desc="" />
            <Param type="int" name="axis" desc="" />
        </Function>
        <Function name="SetGamepadMappings" retType="int" paramCount="1" desc="Set internal gamepad mappings (SDL_GameControllerDB)">
            <Param type="const char *" name="mappings" desc="" />
        </Function>
        <Function name="IsMouseButtonPressed" retType="bool" paramCount="1" desc="Check if a mouse button has been pressed once">
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsMouseButtonDown" retType="bool" paramCount="1" desc="Check if a mouse button is being pressed">
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsMouseButtonReleased" retType="bool" paramCount="1" desc="Check if a mouse button has been released once">
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="IsMouseButtonUp" retType="bool" paramCount="1" desc="Check if a mouse button is NOT being pressed">
            <Param type="int" name="button" desc="" />
        </Function>
        <Function name="GetMouseX" retType="int" paramCount="0" desc="Get mouse position X">
        </Function>
        <Function name="GetMouseY" retType="int" paramCount="0" desc="Get mouse position Y">
        </Function>
        <Function name="GetMousePosition" retType="Vector2" paramCount="0" desc="Get mouse position XY">
        </Function>
        <Function name="GetMouseDelta" retType="Vector2" paramCount="0" desc="Get mouse delta between frames">
        </Function>
        <Function name="SetMousePosition" retType="void" paramCount="2" desc="Set mouse position XY">
            <Param type="int" name="x" desc="" />
            <Param type="int" name="y" desc="" />
        </Function>
        <Function name="SetMouseOffset" retType="void" paramCount="2" desc="Set mouse offset">
            <Param type="int" name="offsetX" desc="" />
            <Param type="int" name="offsetY" desc="" />
        </Function>
        <Function name="SetMouseScale" retType="void" paramCount="2" desc="Set mouse scaling">
            <Param type="float" name="scaleX" desc="" />
            <Param type="float" name="scaleY" desc="" />
        </Function>
        <Function name="GetMouseWheelMove" retType="float" paramCount="0" desc="Get mouse wheel movement for X or Y, whichever is larger">
        </Function>
        <Function name="GetMouseWheelMoveV" retType="Vector2" paramCount="0" desc="Get mouse wheel movement for both X and Y">
        </Function>
        <Function name="SetMouseCursor" retType="void" paramCount="1" desc="Set mouse cursor">
            <Param type="int" name="cursor" desc="" />
        </Function>
        <Function name="GetTouchX" retType="int" paramCount="0" desc="Get touch position X for touch point 0 (relative to screen size)">
        </Function>
        <Function name="GetTouchY" retType="int" paramCount="0" desc="Get touch position Y for touch point 0 (relative to screen size)">
        </Function>
        <Function name="GetTouchPosition" retType="Vector2" paramCount="1" desc="Get touch position XY for a touch point index (relative to screen size)">
            <Param type="int" name="index" desc="" />
        </Function>
        <Function name="GetTouchPointId" retType="int" paramCount="1" desc="Get touch point identifier for given index">
            <Param type="int" name="index" desc="" />
        </Function>
        <Function name="GetTouchPointCount" retType="int" paramCount="0" desc="Get number of touch points">
        </Function>
        <Function name="SetGesturesEnabled" retType="void" paramCount="1" desc="Enable a set of gestures using flags">
            <Param type="unsigned int" name="flags" desc="" />
        </Function>
        <Function name="IsGestureDetected" retType="bool" paramCount="1" desc="Check if a gesture have been detected">
            <Param type="int" name="gesture" desc="" />
        </Function>
        <Function name="GetGestureDetected" retType="int" paramCount="0" desc="Get latest detected gesture">
        </Function>
        <Function name="GetGestureHoldDuration" retType="float" paramCount="0" desc="Get gesture hold time in milliseconds">
        </Function>
        <Function name="GetGestureDragVector" retType="Vector2" paramCount="0" desc="Get gesture drag vector">
        </Function>
        <Function name="GetGestureDragAngle" retType="float" paramCount="0" desc="Get gesture drag angle">
        </Function>
        <Function name="GetGesturePinchVector" retType="Vector2" paramCount="0" desc="Get gesture pinch delta">
        </Function>
        <Function name="GetGesturePinchAngle" retType="float" paramCount="0" desc="Get gesture pinch angle">
        </Function>
        <Function name="UpdateCamera" retType="void" paramCount="2" desc="Update camera position for selected mode">
            <Param type="Camera *" name="camera" desc="" />
            <Param type="int" name="mode" desc="" />
        </Function>
        <Function name="UpdateCameraPro" retType="void" paramCount="4" desc="Update camera movement/rotation">
            <Param type="Camera *" name="camera" desc="" />
            <Param type="Vector3" name="movement" desc="" />
            <Param type="Vector3" name="rotation" desc="" />
            <Param type="float" name="zoom" desc="" />
        </Function>
        <Function name="SetShapesTexture" retType="void" paramCount="2" desc="Set texture and rectangle to be used on shapes drawing">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="source" desc="" />
        </Function>
        <Function name="DrawPixel" retType="void" paramCount="3" desc="Draw a pixel">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPixelV" retType="void" paramCount="2" desc="Draw a pixel (Vector version)">
            <Param type="Vector2" name="position" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLine" retType="void" paramCount="5" desc="Draw a line">
            <Param type="int" name="startPosX" desc="" />
            <Param type="int" name="startPosY" desc="" />
            <Param type="int" name="endPosX" desc="" />
            <Param type="int" name="endPosY" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineV" retType="void" paramCount="3" desc="Draw a line (Vector version)">
            <Param type="Vector2" name="startPos" desc="" />
            <Param type="Vector2" name="endPos" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineEx" retType="void" paramCount="4" desc="Draw a line defining thickness">
            <Param type="Vector2" name="startPos" desc="" />
            <Param type="Vector2" name="endPos" desc="" />
            <Param type="float" name="thick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineBezier" retType="void" paramCount="4" desc="Draw a line using cubic-bezier curves in-out">
            <Param type="Vector2" name="startPos" desc="" />
            <Param type="Vector2" name="endPos" desc="" />
            <Param type="float" name="thick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineBezierQuad" retType="void" paramCount="5" desc="Draw line using quadratic bezier curves with a control point">
            <Param type="Vector2" name="startPos" desc="" />
            <Param type="Vector2" name="endPos" desc="" />
            <Param type="Vector2" name="controlPos" desc="" />
            <Param type="float" name="thick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineBezierCubic" retType="void" paramCount="6" desc="Draw line using cubic bezier curves with 2 control points">
            <Param type="Vector2" name="startPos" desc="" />
            <Param type="Vector2" name="endPos" desc="" />
            <Param type="Vector2" name="startControlPos" desc="" />
            <Param type="Vector2" name="endControlPos" desc="" />
            <Param type="float" name="thick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawLineStrip" retType="void" paramCount="3" desc="Draw lines sequence">
            <Param type="Vector2 *" name="points" desc="" />
            <Param type="int" name="pointCount" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircle" retType="void" paramCount="4" desc="Draw a color-filled circle">
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircleSector" retType="void" paramCount="6" desc="Draw a piece of a circle">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="startAngle" desc="" />
            <Param type="float" name="endAngle" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircleSectorLines" retType="void" paramCount="6" desc="Draw circle sector outline">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="startAngle" desc="" />
            <Param type="float" name="endAngle" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircleGradient" retType="void" paramCount="5" desc="Draw a gradient-filled circle">
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Color" name="color1" desc="" />
            <Param type="Color" name="color2" desc="" />
        </Function>
        <Function name="DrawCircleV" retType="void" paramCount="3" desc="Draw a color-filled circle (Vector version)">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircleLines" retType="void" paramCount="4" desc="Draw circle outline">
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawEllipse" retType="void" paramCount="5" desc="Draw ellipse">
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="float" name="radiusH" desc="" />
            <Param type="float" name="radiusV" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawEllipseLines" retType="void" paramCount="5" desc="Draw ellipse outline">
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="float" name="radiusH" desc="" />
            <Param type="float" name="radiusV" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRing" retType="void" paramCount="7" desc="Draw ring">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="innerRadius" desc="" />
            <Param type="float" name="outerRadius" desc="" />
            <Param type="float" name="startAngle" desc="" />
            <Param type="float" name="endAngle" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRingLines" retType="void" paramCount="7" desc="Draw ring outline">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="innerRadius" desc="" />
            <Param type="float" name="outerRadius" desc="" />
            <Param type="float" name="startAngle" desc="" />
            <Param type="float" name="endAngle" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangle" retType="void" paramCount="5" desc="Draw a color-filled rectangle">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleV" retType="void" paramCount="3" desc="Draw a color-filled rectangle (Vector version)">
            <Param type="Vector2" name="position" desc="" />
            <Param type="Vector2" name="size" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleRec" retType="void" paramCount="2" desc="Draw a color-filled rectangle">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectanglePro" retType="void" paramCount="4" desc="Draw a color-filled rectangle with pro parameters">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="Vector2" name="origin" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleGradientV" retType="void" paramCount="6" desc="Draw a vertical-gradient-filled rectangle">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color1" desc="" />
            <Param type="Color" name="color2" desc="" />
        </Function>
        <Function name="DrawRectangleGradientH" retType="void" paramCount="6" desc="Draw a horizontal-gradient-filled rectangle">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color1" desc="" />
            <Param type="Color" name="color2" desc="" />
        </Function>
        <Function name="DrawRectangleGradientEx" retType="void" paramCount="5" desc="Draw a gradient-filled rectangle with custom vertex colors">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="Color" name="col1" desc="" />
            <Param type="Color" name="col2" desc="" />
            <Param type="Color" name="col3" desc="" />
            <Param type="Color" name="col4" desc="" />
        </Function>
        <Function name="DrawRectangleLines" retType="void" paramCount="5" desc="Draw rectangle outline">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleLinesEx" retType="void" paramCount="3" desc="Draw rectangle outline with extended parameters">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="float" name="lineThick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleRounded" retType="void" paramCount="4" desc="Draw rectangle with rounded edges">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="float" name="roundness" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRectangleRoundedLines" retType="void" paramCount="5" desc="Draw rectangle with rounded edges outline">
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="float" name="roundness" desc="" />
            <Param type="int" name="segments" desc="" />
            <Param type="float" name="lineThick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangle" retType="void" paramCount="4" desc="Draw a color-filled triangle (vertex in counter-clockwise order!)">
            <Param type="Vector2" name="v1" desc="" />
            <Param type="Vector2" name="v2" desc="" />
            <Param type="Vector2" name="v3" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangleLines" retType="void" paramCount="4" desc="Draw triangle outline (vertex in counter-clockwise order!)">
            <Param type="Vector2" name="v1" desc="" />
            <Param type="Vector2" name="v2" desc="" />
            <Param type="Vector2" name="v3" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangleFan" retType="void" paramCount="3" desc="Draw a triangle fan defined by points (first vertex is the center)">
            <Param type="Vector2 *" name="points" desc="" />
            <Param type="int" name="pointCount" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangleStrip" retType="void" paramCount="3" desc="Draw a triangle strip defined by points">
            <Param type="Vector2 *" name="points" desc="" />
            <Param type="int" name="pointCount" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPoly" retType="void" paramCount="5" desc="Draw a regular polygon (Vector version)">
            <Param type="Vector2" name="center" desc="" />
            <Param type="int" name="sides" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPolyLines" retType="void" paramCount="5" desc="Draw a polygon outline of n sides">
            <Param type="Vector2" name="center" desc="" />
            <Param type="int" name="sides" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPolyLinesEx" retType="void" paramCount="6" desc="Draw a polygon outline of n sides with extended parameters">
            <Param type="Vector2" name="center" desc="" />
            <Param type="int" name="sides" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="float" name="lineThick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="CheckCollisionRecs" retType="bool" paramCount="2" desc="Check collision between two rectangles">
            <Param type="Rectangle" name="rec1" desc="" />
            <Param type="Rectangle" name="rec2" desc="" />
        </Function>
        <Function name="CheckCollisionCircles" retType="bool" paramCount="4" desc="Check collision between two circles">
            <Param type="Vector2" name="center1" desc="" />
            <Param type="float" name="radius1" desc="" />
            <Param type="Vector2" name="center2" desc="" />
            <Param type="float" name="radius2" desc="" />
        </Function>
        <Function name="CheckCollisionCircleRec" retType="bool" paramCount="3" desc="Check collision between circle and rectangle">
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
        </Function>
        <Function name="CheckCollisionPointRec" retType="bool" paramCount="2" desc="Check if point is inside rectangle">
            <Param type="Vector2" name="point" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
        </Function>
        <Function name="CheckCollisionPointCircle" retType="bool" paramCount="3" desc="Check if point is inside circle">
            <Param type="Vector2" name="point" desc="" />
            <Param type="Vector2" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
        </Function>
        <Function name="CheckCollisionPointTriangle" retType="bool" paramCount="4" desc="Check if point is inside a triangle">
            <Param type="Vector2" name="point" desc="" />
            <Param type="Vector2" name="p1" desc="" />
            <Param type="Vector2" name="p2" desc="" />
            <Param type="Vector2" name="p3" desc="" />
        </Function>
        <Function name="CheckCollisionPointPoly" retType="bool" paramCount="3" desc="Check if point is within a polygon described by array of vertices">
            <Param type="Vector2" name="point" desc="" />
            <Param type="Vector2 *" name="points" desc="" />
            <Param type="int" name="pointCount" desc="" />
        </Function>
        <Function name="CheckCollisionLines" retType="bool" paramCount="5" desc="Check the collision between two lines defined by two points each, returns collision point by reference">
            <Param type="Vector2" name="startPos1" desc="" />
            <Param type="Vector2" name="endPos1" desc="" />
            <Param type="Vector2" name="startPos2" desc="" />
            <Param type="Vector2" name="endPos2" desc="" />
            <Param type="Vector2 *" name="collisionPoint" desc="" />
        </Function>
        <Function name="CheckCollisionPointLine" retType="bool" paramCount="4" desc="Check if point belongs to line created between two points [p1] and [p2] with defined margin in pixels [threshold]">
            <Param type="Vector2" name="point" desc="" />
            <Param type="Vector2" name="p1" desc="" />
            <Param type="Vector2" name="p2" desc="" />
            <Param type="int" name="threshold" desc="" />
        </Function>
        <Function name="GetCollisionRec" retType="Rectangle" paramCount="2" desc="Get collision rectangle for two rectangles collision">
            <Param type="Rectangle" name="rec1" desc="" />
            <Param type="Rectangle" name="rec2" desc="" />
        </Function>
        <Function name="LoadImage" retType="Image" paramCount="1" desc="Load image from file into CPU memory (RAM)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadImageRaw" retType="Image" paramCount="5" desc="Load image from RAW file data">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="int" name="format" desc="" />
            <Param type="int" name="headerSize" desc="" />
        </Function>
        <Function name="LoadImageAnim" retType="Image" paramCount="2" desc="Load image sequence from file (frames appended to image.data)">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="int *" name="frames" desc="" />
        </Function>
        <Function name="LoadImageFromMemory" retType="Image" paramCount="3" desc="Load image from memory buffer, fileType refers to extension: i.e. '.png'">
            <Param type="const char *" name="fileType" desc="" />
            <Param type="const unsigned char *" name="fileData" desc="" />
            <Param type="int" name="dataSize" desc="" />
        </Function>
        <Function name="LoadImageFromTexture" retType="Image" paramCount="1" desc="Load image from GPU texture data">
            <Param type="Texture2D" name="texture" desc="" />
        </Function>
        <Function name="LoadImageFromScreen" retType="Image" paramCount="0" desc="Load image from screen buffer and (screenshot)">
        </Function>
        <Function name="IsImageReady" retType="bool" paramCount="1" desc="Check if an image is ready">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="UnloadImage" retType="void" paramCount="1" desc="Unload image from CPU memory (RAM)">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="ExportImage" retType="bool" paramCount="2" desc="Export image data to file, returns true on success">
            <Param type="Image" name="image" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="ExportImageAsCode" retType="bool" paramCount="2" desc="Export image as code file defining an array of bytes, returns true on success">
            <Param type="Image" name="image" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="GenImageColor" retType="Image" paramCount="3" desc="Generate image: plain color">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="GenImageGradientV" retType="Image" paramCount="4" desc="Generate image: vertical gradient">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="top" desc="" />
            <Param type="Color" name="bottom" desc="" />
        </Function>
        <Function name="GenImageGradientH" retType="Image" paramCount="4" desc="Generate image: horizontal gradient">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="left" desc="" />
            <Param type="Color" name="right" desc="" />
        </Function>
        <Function name="GenImageGradientRadial" retType="Image" paramCount="5" desc="Generate image: radial gradient">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="float" name="density" desc="" />
            <Param type="Color" name="inner" desc="" />
            <Param type="Color" name="outer" desc="" />
        </Function>
        <Function name="GenImageChecked" retType="Image" paramCount="6" desc="Generate image: checked">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="int" name="checksX" desc="" />
            <Param type="int" name="checksY" desc="" />
            <Param type="Color" name="col1" desc="" />
            <Param type="Color" name="col2" desc="" />
        </Function>
        <Function name="GenImageWhiteNoise" retType="Image" paramCount="3" desc="Generate image: white noise">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="float" name="factor" desc="" />
        </Function>
        <Function name="GenImagePerlinNoise" retType="Image" paramCount="5" desc="Generate image: perlin noise">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="int" name="offsetX" desc="" />
            <Param type="int" name="offsetY" desc="" />
            <Param type="float" name="scale" desc="" />
        </Function>
        <Function name="GenImageCellular" retType="Image" paramCount="3" desc="Generate image: cellular algorithm, bigger tileSize means bigger cells">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="int" name="tileSize" desc="" />
        </Function>
        <Function name="GenImageText" retType="Image" paramCount="3" desc="Generate image: grayscale image from text data">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="ImageCopy" retType="Image" paramCount="1" desc="Create an image duplicate (useful for transformations)">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="ImageFromImage" retType="Image" paramCount="2" desc="Create an image from another image piece">
            <Param type="Image" name="image" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
        </Function>
        <Function name="ImageText" retType="Image" paramCount="3" desc="Create an image from text (default font)">
            <Param type="const char *" name="text" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageTextEx" retType="Image" paramCount="5" desc="Create an image from text (custom sprite font)">
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="ImageFormat" retType="void" paramCount="2" desc="Convert image data to desired format">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="newFormat" desc="" />
        </Function>
        <Function name="ImageToPOT" retType="void" paramCount="2" desc="Convert image to POT (power-of-two)">
            <Param type="Image *" name="image" desc="" />
            <Param type="Color" name="fill" desc="" />
        </Function>
        <Function name="ImageCrop" retType="void" paramCount="2" desc="Crop an image to a defined rectangle">
            <Param type="Image *" name="image" desc="" />
            <Param type="Rectangle" name="crop" desc="" />
        </Function>
        <Function name="ImageAlphaCrop" retType="void" paramCount="2" desc="Crop image depending on alpha value">
            <Param type="Image *" name="image" desc="" />
            <Param type="float" name="threshold" desc="" />
        </Function>
        <Function name="ImageAlphaClear" retType="void" paramCount="3" desc="Clear alpha channel to desired color">
            <Param type="Image *" name="image" desc="" />
            <Param type="Color" name="color" desc="" />
            <Param type="float" name="threshold" desc="" />
        </Function>
        <Function name="ImageAlphaMask" retType="void" paramCount="2" desc="Apply alpha mask to image">
            <Param type="Image *" name="image" desc="" />
            <Param type="Image" name="alphaMask" desc="" />
        </Function>
        <Function name="ImageAlphaPremultiply" retType="void" paramCount="1" desc="Premultiply alpha channel">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageBlurGaussian" retType="void" paramCount="2" desc="Apply Gaussian blur using a box blur approximation">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="blurSize" desc="" />
        </Function>
        <Function name="ImageResize" retType="void" paramCount="3" desc="Resize image (Bicubic scaling algorithm)">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="newWidth" desc="" />
            <Param type="int" name="newHeight" desc="" />
        </Function>
        <Function name="ImageResizeNN" retType="void" paramCount="3" desc="Resize image (Nearest-Neighbor scaling algorithm)">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="newWidth" desc="" />
            <Param type="int" name="newHeight" desc="" />
        </Function>
        <Function name="ImageResizeCanvas" retType="void" paramCount="6" desc="Resize canvas and fill with color">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="newWidth" desc="" />
            <Param type="int" name="newHeight" desc="" />
            <Param type="int" name="offsetX" desc="" />
            <Param type="int" name="offsetY" desc="" />
            <Param type="Color" name="fill" desc="" />
        </Function>
        <Function name="ImageMipmaps" retType="void" paramCount="1" desc="Compute all mipmap levels for a provided image">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageDither" retType="void" paramCount="5" desc="Dither image data to 16bpp or lower (Floyd-Steinberg dithering)">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="rBpp" desc="" />
            <Param type="int" name="gBpp" desc="" />
            <Param type="int" name="bBpp" desc="" />
            <Param type="int" name="aBpp" desc="" />
        </Function>
        <Function name="ImageFlipVertical" retType="void" paramCount="1" desc="Flip image vertically">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageFlipHorizontal" retType="void" paramCount="1" desc="Flip image horizontally">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageRotateCW" retType="void" paramCount="1" desc="Rotate image clockwise 90deg">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageRotateCCW" retType="void" paramCount="1" desc="Rotate image counter-clockwise 90deg">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageColorTint" retType="void" paramCount="2" desc="Modify image color: tint">
            <Param type="Image *" name="image" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageColorInvert" retType="void" paramCount="1" desc="Modify image color: invert">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageColorGrayscale" retType="void" paramCount="1" desc="Modify image color: grayscale">
            <Param type="Image *" name="image" desc="" />
        </Function>
        <Function name="ImageColorContrast" retType="void" paramCount="2" desc="Modify image color: contrast (-100 to 100)">
            <Param type="Image *" name="image" desc="" />
            <Param type="float" name="contrast" desc="" />
        </Function>
        <Function name="ImageColorBrightness" retType="void" paramCount="2" desc="Modify image color: brightness (-255 to 255)">
            <Param type="Image *" name="image" desc="" />
            <Param type="int" name="brightness" desc="" />
        </Function>
        <Function name="ImageColorReplace" retType="void" paramCount="3" desc="Modify image color: replace color">
            <Param type="Image *" name="image" desc="" />
            <Param type="Color" name="color" desc="" />
            <Param type="Color" name="replace" desc="" />
        </Function>
        <Function name="LoadImageColors" retType="Color *" paramCount="1" desc="Load color data from image as a Color array (RGBA - 32bit)">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="LoadImagePalette" retType="Color *" paramCount="3" desc="Load colors palette from image as a Color array (RGBA - 32bit)">
            <Param type="Image" name="image" desc="" />
            <Param type="int" name="maxPaletteSize" desc="" />
            <Param type="int *" name="colorCount" desc="" />
        </Function>
        <Function name="UnloadImageColors" retType="void" paramCount="1" desc="Unload color data loaded with LoadImageColors()">
            <Param type="Color *" name="colors" desc="" />
        </Function>
        <Function name="UnloadImagePalette" retType="void" paramCount="1" desc="Unload colors palette loaded with LoadImagePalette()">
            <Param type="Color *" name="colors" desc="" />
        </Function>
        <Function name="GetImageAlphaBorder" retType="Rectangle" paramCount="2" desc="Get image alpha border rectangle">
            <Param type="Image" name="image" desc="" />
            <Param type="float" name="threshold" desc="" />
        </Function>
        <Function name="GetImageColor" retType="Color" paramCount="3" desc="Get image pixel color at (x, y) position">
            <Param type="Image" name="image" desc="" />
            <Param type="int" name="x" desc="" />
            <Param type="int" name="y" desc="" />
        </Function>
        <Function name="ImageClearBackground" retType="void" paramCount="2" desc="Clear image background with given color">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawPixel" retType="void" paramCount="4" desc="Draw pixel within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawPixelV" retType="void" paramCount="3" desc="Draw pixel within an image (Vector version)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawLine" retType="void" paramCount="6" desc="Draw line within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="int" name="startPosX" desc="" />
            <Param type="int" name="startPosY" desc="" />
            <Param type="int" name="endPosX" desc="" />
            <Param type="int" name="endPosY" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawLineV" retType="void" paramCount="4" desc="Draw line within an image (Vector version)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Vector2" name="start" desc="" />
            <Param type="Vector2" name="end" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawCircle" retType="void" paramCount="5" desc="Draw a filled circle within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="int" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawCircleV" retType="void" paramCount="4" desc="Draw a filled circle within an image (Vector version)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Vector2" name="center" desc="" />
            <Param type="int" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawCircleLines" retType="void" paramCount="5" desc="Draw circle outline within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="int" name="centerX" desc="" />
            <Param type="int" name="centerY" desc="" />
            <Param type="int" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawCircleLinesV" retType="void" paramCount="4" desc="Draw circle outline within an image (Vector version)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Vector2" name="center" desc="" />
            <Param type="int" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawRectangle" retType="void" paramCount="6" desc="Draw rectangle within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawRectangleV" retType="void" paramCount="4" desc="Draw rectangle within an image (Vector version)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="Vector2" name="size" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawRectangleRec" retType="void" paramCount="3" desc="Draw rectangle within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawRectangleLines" retType="void" paramCount="4" desc="Draw rectangle lines within an image">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="int" name="thick" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDraw" retType="void" paramCount="5" desc="Draw a source image within a destination image (tint applied to source)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Image" name="src" desc="" />
            <Param type="Rectangle" name="srcRec" desc="" />
            <Param type="Rectangle" name="dstRec" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="ImageDrawText" retType="void" paramCount="6" desc="Draw text (using default font) within an image (destination)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ImageDrawTextEx" retType="void" paramCount="7" desc="Draw text (custom sprite font) within an image (destination)">
            <Param type="Image *" name="dst" desc="" />
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="LoadTexture" retType="Texture2D" paramCount="1" desc="Load texture from file into GPU memory (VRAM)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadTextureFromImage" retType="Texture2D" paramCount="1" desc="Load texture from image data">
            <Param type="Image" name="image" desc="" />
        </Function>
        <Function name="LoadTextureCubemap" retType="TextureCubemap" paramCount="2" desc="Load cubemap from image, multiple image cubemap layouts supported">
            <Param type="Image" name="image" desc="" />
            <Param type="int" name="layout" desc="" />
        </Function>
        <Function name="LoadRenderTexture" retType="RenderTexture2D" paramCount="2" desc="Load texture for rendering (framebuffer)">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
        </Function>
        <Function name="IsTextureReady" retType="bool" paramCount="1" desc="Check if a texture is ready">
            <Param type="Texture2D" name="texture" desc="" />
        </Function>
        <Function name="UnloadTexture" retType="void" paramCount="1" desc="Unload texture from GPU memory (VRAM)">
            <Param type="Texture2D" name="texture" desc="" />
        </Function>
        <Function name="IsRenderTextureReady" retType="bool" paramCount="1" desc="Check if a render texture is ready">
            <Param type="RenderTexture2D" name="target" desc="" />
        </Function>
        <Function name="UnloadRenderTexture" retType="void" paramCount="1" desc="Unload render texture from GPU memory (VRAM)">
            <Param type="RenderTexture2D" name="target" desc="" />
        </Function>
        <Function name="UpdateTexture" retType="void" paramCount="2" desc="Update GPU texture with new data">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="const void *" name="pixels" desc="" />
        </Function>
        <Function name="UpdateTextureRec" retType="void" paramCount="3" desc="Update GPU texture rectangle with new data">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="rec" desc="" />
            <Param type="const void *" name="pixels" desc="" />
        </Function>
        <Function name="GenTextureMipmaps" retType="void" paramCount="1" desc="Generate GPU mipmaps for a texture">
            <Param type="Texture2D *" name="texture" desc="" />
        </Function>
        <Function name="SetTextureFilter" retType="void" paramCount="2" desc="Set texture scaling filter mode">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="int" name="filter" desc="" />
        </Function>
        <Function name="SetTextureWrap" retType="void" paramCount="2" desc="Set texture wrapping mode">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="int" name="wrap" desc="" />
        </Function>
        <Function name="DrawTexture" retType="void" paramCount="4" desc="Draw a Texture2D">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextureV" retType="void" paramCount="3" desc="Draw a Texture2D with position defined as Vector2">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextureEx" retType="void" paramCount="5" desc="Draw a Texture2D with extended parameters">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="float" name="scale" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextureRec" retType="void" paramCount="4" desc="Draw a part of a texture defined by a rectangle">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="source" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTexturePro" retType="void" paramCount="6" desc="Draw a part of a texture defined by a rectangle with 'pro' parameters">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="source" desc="" />
            <Param type="Rectangle" name="dest" desc="" />
            <Param type="Vector2" name="origin" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextureNPatch" retType="void" paramCount="6" desc="Draws a texture (or part of it) that stretches or shrinks nicely">
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="NPatchInfo" name="nPatchInfo" desc="" />
            <Param type="Rectangle" name="dest" desc="" />
            <Param type="Vector2" name="origin" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="Fade" retType="Color" paramCount="2" desc="Get color with alpha applied, alpha goes from 0.0f to 1.0f">
            <Param type="Color" name="color" desc="" />
            <Param type="float" name="alpha" desc="" />
        </Function>
        <Function name="ColorToInt" retType="int" paramCount="1" desc="Get hexadecimal value for a Color">
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ColorNormalize" retType="Vector4" paramCount="1" desc="Get Color normalized as float [0..1]">
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ColorFromNormalized" retType="Color" paramCount="1" desc="Get Color from normalized values [0..1]">
            <Param type="Vector4" name="normalized" desc="" />
        </Function>
        <Function name="ColorToHSV" retType="Vector3" paramCount="1" desc="Get HSV values for a Color, hue [0..360], saturation/value [0..1]">
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="ColorFromHSV" retType="Color" paramCount="3" desc="Get a Color from HSV values, hue [0..360], saturation/value [0..1]">
            <Param type="float" name="hue" desc="" />
            <Param type="float" name="saturation" desc="" />
            <Param type="float" name="value" desc="" />
        </Function>
        <Function name="ColorTint" retType="Color" paramCount="2" desc="Get color multiplied with another color">
            <Param type="Color" name="color" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="ColorBrightness" retType="Color" paramCount="2" desc="Get color with brightness correction, brightness factor goes from -1.0f to 1.0f">
            <Param type="Color" name="color" desc="" />
            <Param type="float" name="factor" desc="" />
        </Function>
        <Function name="ColorContrast" retType="Color" paramCount="2" desc="Get color with contrast correction, contrast values between -1.0f and 1.0f">
            <Param type="Color" name="color" desc="" />
            <Param type="float" name="contrast" desc="" />
        </Function>
        <Function name="ColorAlpha" retType="Color" paramCount="2" desc="Get color with alpha applied, alpha goes from 0.0f to 1.0f">
            <Param type="Color" name="color" desc="" />
            <Param type="float" name="alpha" desc="" />
        </Function>
        <Function name="ColorAlphaBlend" retType="Color" paramCount="3" desc="Get src alpha-blended into dst color with tint">
            <Param type="Color" name="dst" desc="" />
            <Param type="Color" name="src" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="GetColor" retType="Color" paramCount="1" desc="Get Color structure from hexadecimal value">
            <Param type="unsigned int" name="hexValue" desc="" />
        </Function>
        <Function name="GetPixelColor" retType="Color" paramCount="2" desc="Get Color from a source pixel pointer of certain format">
            <Param type="void *" name="srcPtr" desc="" />
            <Param type="int" name="format" desc="" />
        </Function>
        <Function name="SetPixelColor" retType="void" paramCount="3" desc="Set color formatted into destination pixel pointer">
            <Param type="void *" name="dstPtr" desc="" />
            <Param type="Color" name="color" desc="" />
            <Param type="int" name="format" desc="" />
        </Function>
        <Function name="GetPixelDataSize" retType="int" paramCount="3" desc="Get pixel data size in bytes for certain format">
            <Param type="int" name="width" desc="" />
            <Param type="int" name="height" desc="" />
            <Param type="int" name="format" desc="" />
        </Function>
        <Function name="GetFontDefault" retType="Font" paramCount="0" desc="Get the default Font">
        </Function>
        <Function name="LoadFont" retType="Font" paramCount="1" desc="Load font from file into GPU memory (VRAM)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadFontEx" retType="Font" paramCount="4" desc="Load font from file with extended parameters, use NULL for fontChars and 0 for glyphCount to load the default character set">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="int *" name="fontChars" desc="" />
            <Param type="int" name="glyphCount" desc="" />
        </Function>
        <Function name="LoadFontFromImage" retType="Font" paramCount="3" desc="Load font from Image (XNA style)">
            <Param type="Image" name="image" desc="" />
            <Param type="Color" name="key" desc="" />
            <Param type="int" name="firstChar" desc="" />
        </Function>
        <Function name="LoadFontFromMemory" retType="Font" paramCount="6" desc="Load font from memory buffer, fileType refers to extension: i.e. '.ttf'">
            <Param type="const char *" name="fileType" desc="" />
            <Param type="const unsigned char *" name="fileData" desc="" />
            <Param type="int" name="dataSize" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="int *" name="fontChars" desc="" />
            <Param type="int" name="glyphCount" desc="" />
        </Function>
        <Function name="IsFontReady" retType="bool" paramCount="1" desc="Check if a font is ready">
            <Param type="Font" name="font" desc="" />
        </Function>
        <Function name="LoadFontData" retType="GlyphInfo *" paramCount="6" desc="Load font data for further use">
            <Param type="const unsigned char *" name="fileData" desc="" />
            <Param type="int" name="dataSize" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="int *" name="fontChars" desc="" />
            <Param type="int" name="glyphCount" desc="" />
            <Param type="int" name="type" desc="" />
        </Function>
        <Function name="GenImageFontAtlas" retType="Image" paramCount="6" desc="Generate image font atlas using chars info">
            <Param type="const GlyphInfo *" name="chars" desc="" />
            <Param type="Rectangle **" name="recs" desc="" />
            <Param type="int" name="glyphCount" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="int" name="padding" desc="" />
            <Param type="int" name="packMethod" desc="" />
        </Function>
        <Function name="UnloadFontData" retType="void" paramCount="2" desc="Unload font chars info data (RAM)">
            <Param type="GlyphInfo *" name="chars" desc="" />
            <Param type="int" name="glyphCount" desc="" />
        </Function>
        <Function name="UnloadFont" retType="void" paramCount="1" desc="Unload font from GPU memory (VRAM)">
            <Param type="Font" name="font" desc="" />
        </Function>
        <Function name="ExportFontAsCode" retType="bool" paramCount="2" desc="Export font as code file, returns true on success">
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="DrawFPS" retType="void" paramCount="2" desc="Draw current FPS">
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
        </Function>
        <Function name="DrawText" retType="void" paramCount="5" desc="Draw text (using default font)">
            <Param type="const char *" name="text" desc="" />
            <Param type="int" name="posX" desc="" />
            <Param type="int" name="posY" desc="" />
            <Param type="int" name="fontSize" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTextEx" retType="void" paramCount="6" desc="Draw text using font and additional parameters">
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextPro" retType="void" paramCount="8" desc="Draw text using Font and pro parameters (rotation)">
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="Vector2" name="origin" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextCodepoint" retType="void" paramCount="5" desc="Draw one character (codepoint)">
            <Param type="Font" name="font" desc="" />
            <Param type="int" name="codepoint" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawTextCodepoints" retType="void" paramCount="7" desc="Draw multiple character (codepoint)">
            <Param type="Font" name="font" desc="" />
            <Param type="const int *" name="codepoints" desc="" />
            <Param type="int" name="count" desc="" />
            <Param type="Vector2" name="position" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="MeasureText" retType="int" paramCount="2" desc="Measure string width for default font">
            <Param type="const char *" name="text" desc="" />
            <Param type="int" name="fontSize" desc="" />
        </Function>
        <Function name="MeasureTextEx" retType="Vector2" paramCount="4" desc="Measure string size for Font">
            <Param type="Font" name="font" desc="" />
            <Param type="const char *" name="text" desc="" />
            <Param type="float" name="fontSize" desc="" />
            <Param type="float" name="spacing" desc="" />
        </Function>
        <Function name="GetGlyphIndex" retType="int" paramCount="2" desc="Get glyph index position in font for a codepoint (unicode character), fallback to '?' if not found">
            <Param type="Font" name="font" desc="" />
            <Param type="int" name="codepoint" desc="" />
        </Function>
        <Function name="GetGlyphInfo" retType="GlyphInfo" paramCount="2" desc="Get glyph font info data for a codepoint (unicode character), fallback to '?' if not found">
            <Param type="Font" name="font" desc="" />
            <Param type="int" name="codepoint" desc="" />
        </Function>
        <Function name="GetGlyphAtlasRec" retType="Rectangle" paramCount="2" desc="Get glyph rectangle in font atlas for a codepoint (unicode character), fallback to '?' if not found">
            <Param type="Font" name="font" desc="" />
            <Param type="int" name="codepoint" desc="" />
        </Function>
        <Function name="LoadUTF8" retType="char *" paramCount="2" desc="Load UTF-8 text encoded from codepoints array">
            <Param type="const int *" name="codepoints" desc="" />
            <Param type="int" name="length" desc="" />
        </Function>
        <Function name="UnloadUTF8" retType="void" paramCount="1" desc="Unload UTF-8 text encoded from codepoints array">
            <Param type="char *" name="text" desc="" />
        </Function>
        <Function name="LoadCodepoints" retType="int *" paramCount="2" desc="Load all codepoints from a UTF-8 text string, codepoints count returned by parameter">
            <Param type="const char *" name="text" desc="" />
            <Param type="int *" name="count" desc="" />
        </Function>
        <Function name="UnloadCodepoints" retType="void" paramCount="1" desc="Unload codepoints data from memory">
            <Param type="int *" name="codepoints" desc="" />
        </Function>
        <Function name="GetCodepointCount" retType="int" paramCount="1" desc="Get total number of codepoints in a UTF-8 encoded string">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="GetCodepoint" retType="int" paramCount="2" desc="Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
            <Param type="const char *" name="text" desc="" />
            <Param type="int *" name="codepointSize" desc="" />
        </Function>
        <Function name="GetCodepointNext" retType="int" paramCount="2" desc="Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
            <Param type="const char *" name="text" desc="" />
            <Param type="int *" name="codepointSize" desc="" />
        </Function>
        <Function name="GetCodepointPrevious" retType="int" paramCount="2" desc="Get previous codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
            <Param type="const char *" name="text" desc="" />
            <Param type="int *" name="codepointSize" desc="" />
        </Function>
        <Function name="CodepointToUTF8" retType="const char *" paramCount="2" desc="Encode one codepoint into UTF-8 byte array (array length returned as parameter)">
            <Param type="int" name="codepoint" desc="" />
            <Param type="int *" name="utf8Size" desc="" />
        </Function>
        <Function name="TextCopy" retType="int" paramCount="2" desc="Copy one string to another, returns bytes copied">
            <Param type="char *" name="dst" desc="" />
            <Param type="const char *" name="src" desc="" />
        </Function>
        <Function name="TextIsEqual" retType="bool" paramCount="2" desc="Check if two text string are equal">
            <Param type="const char *" name="text1" desc="" />
            <Param type="const char *" name="text2" desc="" />
        </Function>
        <Function name="TextLength" retType="unsigned int" paramCount="1" desc="Get text length, checks for '\0' ending">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="TextFormat" retType="const char *" paramCount="2" desc="Text formatting with variables (sprintf() style)">
            <Param type="const char *" name="text" desc="" />
            <Param type="..." name="args" desc="" />
        </Function>
        <Function name="TextSubtext" retType="const char *" paramCount="3" desc="Get a piece of a text string">
            <Param type="const char *" name="text" desc="" />
            <Param type="int" name="position" desc="" />
            <Param type="int" name="length" desc="" />
        </Function>
        <Function name="TextReplace" retType="char *" paramCount="3" desc="Replace text string (WARNING: memory must be freed!)">
            <Param type="char *" name="text" desc="" />
            <Param type="const char *" name="replace" desc="" />
            <Param type="const char *" name="by" desc="" />
        </Function>
        <Function name="TextInsert" retType="char *" paramCount="3" desc="Insert text in a position (WARNING: memory must be freed!)">
            <Param type="const char *" name="text" desc="" />
            <Param type="const char *" name="insert" desc="" />
            <Param type="int" name="position" desc="" />
        </Function>
        <Function name="TextJoin" retType="const char *" paramCount="3" desc="Join text strings with delimiter">
            <Param type="const char **" name="textList" desc="" />
            <Param type="int" name="count" desc="" />
            <Param type="const char *" name="delimiter" desc="" />
        </Function>
        <Function name="TextSplit" retType="const char **" paramCount="3" desc="Split text into multiple strings">
            <Param type="const char *" name="text" desc="" />
            <Param type="char" name="delimiter" desc="" />
            <Param type="int *" name="count" desc="" />
        </Function>
        <Function name="TextAppend" retType="void" paramCount="3" desc="Append text at specific position and move cursor!">
            <Param type="char *" name="text" desc="" />
            <Param type="const char *" name="append" desc="" />
            <Param type="int *" name="position" desc="" />
        </Function>
        <Function name="TextFindIndex" retType="int" paramCount="2" desc="Find first text occurrence within a string">
            <Param type="const char *" name="text" desc="" />
            <Param type="const char *" name="find" desc="" />
        </Function>
        <Function name="TextToUpper" retType="const char *" paramCount="1" desc="Get upper case version of provided string">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="TextToLower" retType="const char *" paramCount="1" desc="Get lower case version of provided string">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="TextToPascal" retType="const char *" paramCount="1" desc="Get Pascal case notation version of provided string">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="TextToInteger" retType="int" paramCount="1" desc="Get integer value from text (negative values not supported)">
            <Param type="const char *" name="text" desc="" />
        </Function>
        <Function name="DrawLine3D" retType="void" paramCount="3" desc="Draw a line in 3D world space">
            <Param type="Vector3" name="startPos" desc="" />
            <Param type="Vector3" name="endPos" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPoint3D" retType="void" paramCount="2" desc="Draw a point in 3D space, actually a small line">
            <Param type="Vector3" name="position" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCircle3D" retType="void" paramCount="5" desc="Draw a circle in 3D world space">
            <Param type="Vector3" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Vector3" name="rotationAxis" desc="" />
            <Param type="float" name="rotationAngle" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangle3D" retType="void" paramCount="4" desc="Draw a color-filled triangle (vertex in counter-clockwise order!)">
            <Param type="Vector3" name="v1" desc="" />
            <Param type="Vector3" name="v2" desc="" />
            <Param type="Vector3" name="v3" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawTriangleStrip3D" retType="void" paramCount="3" desc="Draw a triangle strip defined by points">
            <Param type="Vector3 *" name="points" desc="" />
            <Param type="int" name="pointCount" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCube" retType="void" paramCount="5" desc="Draw cube">
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="width" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="float" name="length" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCubeV" retType="void" paramCount="3" desc="Draw cube (Vector version)">
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector3" name="size" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCubeWires" retType="void" paramCount="5" desc="Draw cube wires">
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="width" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="float" name="length" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCubeWiresV" retType="void" paramCount="3" desc="Draw cube wires (Vector version)">
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector3" name="size" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawSphere" retType="void" paramCount="3" desc="Draw sphere">
            <Param type="Vector3" name="centerPos" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawSphereEx" retType="void" paramCount="5" desc="Draw sphere with extended parameters">
            <Param type="Vector3" name="centerPos" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawSphereWires" retType="void" paramCount="5" desc="Draw sphere wires">
            <Param type="Vector3" name="centerPos" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCylinder" retType="void" paramCount="6" desc="Draw a cylinder/cone">
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="radiusTop" desc="" />
            <Param type="float" name="radiusBottom" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCylinderEx" retType="void" paramCount="6" desc="Draw a cylinder with base at startPos and top at endPos">
            <Param type="Vector3" name="startPos" desc="" />
            <Param type="Vector3" name="endPos" desc="" />
            <Param type="float" name="startRadius" desc="" />
            <Param type="float" name="endRadius" desc="" />
            <Param type="int" name="sides" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCylinderWires" retType="void" paramCount="6" desc="Draw a cylinder/cone wires">
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="radiusTop" desc="" />
            <Param type="float" name="radiusBottom" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCylinderWiresEx" retType="void" paramCount="6" desc="Draw a cylinder wires with base at startPos and top at endPos">
            <Param type="Vector3" name="startPos" desc="" />
            <Param type="Vector3" name="endPos" desc="" />
            <Param type="float" name="startRadius" desc="" />
            <Param type="float" name="endRadius" desc="" />
            <Param type="int" name="sides" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCapsule" retType="void" paramCount="6" desc="Draw a capsule with the center of its sphere caps at startPos and endPos">
            <Param type="Vector3" name="startPos" desc="" />
            <Param type="Vector3" name="endPos" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawCapsuleWires" retType="void" paramCount="6" desc="Draw capsule wireframe with the center of its sphere caps at startPos and endPos">
            <Param type="Vector3" name="startPos" desc="" />
            <Param type="Vector3" name="endPos" desc="" />
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="slices" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawPlane" retType="void" paramCount="3" desc="Draw a plane XZ">
            <Param type="Vector3" name="centerPos" desc="" />
            <Param type="Vector2" name="size" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawRay" retType="void" paramCount="2" desc="Draw a ray line">
            <Param type="Ray" name="ray" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawGrid" retType="void" paramCount="2" desc="Draw a grid (centered at (0, 0, 0))">
            <Param type="int" name="slices" desc="" />
            <Param type="float" name="spacing" desc="" />
        </Function>
        <Function name="LoadModel" retType="Model" paramCount="1" desc="Load model from files (meshes and materials)">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadModelFromMesh" retType="Model" paramCount="1" desc="Load model from generated mesh (default material)">
            <Param type="Mesh" name="mesh" desc="" />
        </Function>
        <Function name="IsModelReady" retType="bool" paramCount="1" desc="Check if a model is ready">
            <Param type="Model" name="model" desc="" />
        </Function>
        <Function name="UnloadModel" retType="void" paramCount="1" desc="Unload model (including meshes) from memory (RAM and/or VRAM)">
            <Param type="Model" name="model" desc="" />
        </Function>
        <Function name="GetModelBoundingBox" retType="BoundingBox" paramCount="1" desc="Compute model bounding box limits (considers all meshes)">
            <Param type="Model" name="model" desc="" />
        </Function>
        <Function name="DrawModel" retType="void" paramCount="4" desc="Draw a model (with texture if set)">
            <Param type="Model" name="model" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="scale" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawModelEx" retType="void" paramCount="6" desc="Draw a model with extended parameters">
            <Param type="Model" name="model" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector3" name="rotationAxis" desc="" />
            <Param type="float" name="rotationAngle" desc="" />
            <Param type="Vector3" name="scale" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawModelWires" retType="void" paramCount="4" desc="Draw a model wires (with texture if set)">
            <Param type="Model" name="model" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="scale" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawModelWiresEx" retType="void" paramCount="6" desc="Draw a model wires (with texture if set) with extended parameters">
            <Param type="Model" name="model" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector3" name="rotationAxis" desc="" />
            <Param type="float" name="rotationAngle" desc="" />
            <Param type="Vector3" name="scale" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawBoundingBox" retType="void" paramCount="2" desc="Draw bounding box (wires)">
            <Param type="BoundingBox" name="box" desc="" />
            <Param type="Color" name="color" desc="" />
        </Function>
        <Function name="DrawBillboard" retType="void" paramCount="5" desc="Draw a billboard texture">
            <Param type="Camera" name="camera" desc="" />
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="float" name="size" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawBillboardRec" retType="void" paramCount="6" desc="Draw a billboard texture defined by source">
            <Param type="Camera" name="camera" desc="" />
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="source" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector2" name="size" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="DrawBillboardPro" retType="void" paramCount="9" desc="Draw a billboard texture defined by source and rotation">
            <Param type="Camera" name="camera" desc="" />
            <Param type="Texture2D" name="texture" desc="" />
            <Param type="Rectangle" name="source" desc="" />
            <Param type="Vector3" name="position" desc="" />
            <Param type="Vector3" name="up" desc="" />
            <Param type="Vector2" name="size" desc="" />
            <Param type="Vector2" name="origin" desc="" />
            <Param type="float" name="rotation" desc="" />
            <Param type="Color" name="tint" desc="" />
        </Function>
        <Function name="UploadMesh" retType="void" paramCount="2" desc="Upload mesh vertex data in GPU and provide VAO/VBO ids">
            <Param type="Mesh *" name="mesh" desc="" />
            <Param type="bool" name="dynamic" desc="" />
        </Function>
        <Function name="UpdateMeshBuffer" retType="void" paramCount="5" desc="Update mesh vertex data in GPU for a specific buffer index">
            <Param type="Mesh" name="mesh" desc="" />
            <Param type="int" name="index" desc="" />
            <Param type="const void *" name="data" desc="" />
            <Param type="int" name="dataSize" desc="" />
            <Param type="int" name="offset" desc="" />
        </Function>
        <Function name="UnloadMesh" retType="void" paramCount="1" desc="Unload mesh data from CPU and GPU">
            <Param type="Mesh" name="mesh" desc="" />
        </Function>
        <Function name="DrawMesh" retType="void" paramCount="3" desc="Draw a 3d mesh with material and transform">
            <Param type="Mesh" name="mesh" desc="" />
            <Param type="Material" name="material" desc="" />
            <Param type="Matrix" name="transform" desc="" />
        </Function>
        <Function name="DrawMeshInstanced" retType="void" paramCount="4" desc="Draw multiple mesh instances with material and different transforms">
            <Param type="Mesh" name="mesh" desc="" />
            <Param type="Material" name="material" desc="" />
            <Param type="const Matrix *" name="transforms" desc="" />
            <Param type="int" name="instances" desc="" />
        </Function>
        <Function name="ExportMesh" retType="bool" paramCount="2" desc="Export mesh data to file, returns true on success">
            <Param type="Mesh" name="mesh" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="GetMeshBoundingBox" retType="BoundingBox" paramCount="1" desc="Compute mesh bounding box limits">
            <Param type="Mesh" name="mesh" desc="" />
        </Function>
        <Function name="GenMeshTangents" retType="void" paramCount="1" desc="Compute mesh tangents">
            <Param type="Mesh *" name="mesh" desc="" />
        </Function>
        <Function name="GenMeshPoly" retType="Mesh" paramCount="2" desc="Generate polygonal mesh">
            <Param type="int" name="sides" desc="" />
            <Param type="float" name="radius" desc="" />
        </Function>
        <Function name="GenMeshPlane" retType="Mesh" paramCount="4" desc="Generate plane mesh (with subdivisions)">
            <Param type="float" name="width" desc="" />
            <Param type="float" name="length" desc="" />
            <Param type="int" name="resX" desc="" />
            <Param type="int" name="resZ" desc="" />
        </Function>
        <Function name="GenMeshCube" retType="Mesh" paramCount="3" desc="Generate cuboid mesh">
            <Param type="float" name="width" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="float" name="length" desc="" />
        </Function>
        <Function name="GenMeshSphere" retType="Mesh" paramCount="3" desc="Generate sphere mesh (standard sphere)">
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="int" name="slices" desc="" />
        </Function>
        <Function name="GenMeshHemiSphere" retType="Mesh" paramCount="3" desc="Generate half-sphere mesh (no bottom cap)">
            <Param type="float" name="radius" desc="" />
            <Param type="int" name="rings" desc="" />
            <Param type="int" name="slices" desc="" />
        </Function>
        <Function name="GenMeshCylinder" retType="Mesh" paramCount="3" desc="Generate cylinder mesh">
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="int" name="slices" desc="" />
        </Function>
        <Function name="GenMeshCone" retType="Mesh" paramCount="3" desc="Generate cone/pyramid mesh">
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="height" desc="" />
            <Param type="int" name="slices" desc="" />
        </Function>
        <Function name="GenMeshTorus" retType="Mesh" paramCount="4" desc="Generate torus mesh">
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="size" desc="" />
            <Param type="int" name="radSeg" desc="" />
            <Param type="int" name="sides" desc="" />
        </Function>
        <Function name="GenMeshKnot" retType="Mesh" paramCount="4" desc="Generate trefoil knot mesh">
            <Param type="float" name="radius" desc="" />
            <Param type="float" name="size" desc="" />
            <Param type="int" name="radSeg" desc="" />
            <Param type="int" name="sides" desc="" />
        </Function>
        <Function name="GenMeshHeightmap" retType="Mesh" paramCount="2" desc="Generate heightmap mesh from image data">
            <Param type="Image" name="heightmap" desc="" />
            <Param type="Vector3" name="size" desc="" />
        </Function>
        <Function name="GenMeshCubicmap" retType="Mesh" paramCount="2" desc="Generate cubes-based map mesh from image data">
            <Param type="Image" name="cubicmap" desc="" />
            <Param type="Vector3" name="cubeSize" desc="" />
        </Function>
        <Function name="LoadMaterials" retType="Material *" paramCount="2" desc="Load materials from model file">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="int *" name="materialCount" desc="" />
        </Function>
        <Function name="LoadMaterialDefault" retType="Material" paramCount="0" desc="Load default material (Supports: DIFFUSE, SPECULAR, NORMAL maps)">
        </Function>
        <Function name="IsMaterialReady" retType="bool" paramCount="1" desc="Check if a material is ready">
            <Param type="Material" name="material" desc="" />
        </Function>
        <Function name="UnloadMaterial" retType="void" paramCount="1" desc="Unload material from GPU memory (VRAM)">
            <Param type="Material" name="material" desc="" />
        </Function>
        <Function name="SetMaterialTexture" retType="void" paramCount="3" desc="Set texture for a material map type (MATERIAL_MAP_DIFFUSE, MATERIAL_MAP_SPECULAR...)">
            <Param type="Material *" name="material" desc="" />
            <Param type="int" name="mapType" desc="" />
            <Param type="Texture2D" name="texture" desc="" />
        </Function>
        <Function name="SetModelMeshMaterial" retType="void" paramCount="3" desc="Set material for a mesh">
            <Param type="Model *" name="model" desc="" />
            <Param type="int" name="meshId" desc="" />
            <Param type="int" name="materialId" desc="" />
        </Function>
        <Function name="LoadModelAnimations" retType="ModelAnimation *" paramCount="2" desc="Load model animations from file">
            <Param type="const char *" name="fileName" desc="" />
            <Param type="unsigned int *" name="animCount" desc="" />
        </Function>
        <Function name="UpdateModelAnimation" retType="void" paramCount="3" desc="Update model animation pose">
            <Param type="Model" name="model" desc="" />
            <Param type="ModelAnimation" name="anim" desc="" />
            <Param type="int" name="frame" desc="" />
        </Function>
        <Function name="UnloadModelAnimation" retType="void" paramCount="1" desc="Unload animation data">
            <Param type="ModelAnimation" name="anim" desc="" />
        </Function>
        <Function name="UnloadModelAnimations" retType="void" paramCount="2" desc="Unload animation array data">
            <Param type="ModelAnimation *" name="animations" desc="" />
            <Param type="unsigned int" name="count" desc="" />
        </Function>
        <Function name="IsModelAnimationValid" retType="bool" paramCount="2" desc="Check model animation skeleton match">
            <Param type="Model" name="model" desc="" />
            <Param type="ModelAnimation" name="anim" desc="" />
        </Function>
        <Function name="CheckCollisionSpheres" retType="bool" paramCount="4" desc="Check collision between two spheres">
            <Param type="Vector3" name="center1" desc="" />
            <Param type="float" name="radius1" desc="" />
            <Param type="Vector3" name="center2" desc="" />
            <Param type="float" name="radius2" desc="" />
        </Function>
        <Function name="CheckCollisionBoxes" retType="bool" paramCount="2" desc="Check collision between two bounding boxes">
            <Param type="BoundingBox" name="box1" desc="" />
            <Param type="BoundingBox" name="box2" desc="" />
        </Function>
        <Function name="CheckCollisionBoxSphere" retType="bool" paramCount="3" desc="Check collision between box and sphere">
            <Param type="BoundingBox" name="box" desc="" />
            <Param type="Vector3" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
        </Function>
        <Function name="GetRayCollisionSphere" retType="RayCollision" paramCount="3" desc="Get collision info between ray and sphere">
            <Param type="Ray" name="ray" desc="" />
            <Param type="Vector3" name="center" desc="" />
            <Param type="float" name="radius" desc="" />
        </Function>
        <Function name="GetRayCollisionBox" retType="RayCollision" paramCount="2" desc="Get collision info between ray and box">
            <Param type="Ray" name="ray" desc="" />
            <Param type="BoundingBox" name="box" desc="" />
        </Function>
        <Function name="GetRayCollisionMesh" retType="RayCollision" paramCount="3" desc="Get collision info between ray and mesh">
            <Param type="Ray" name="ray" desc="" />
            <Param type="Mesh" name="mesh" desc="" />
            <Param type="Matrix" name="transform" desc="" />
        </Function>
        <Function name="GetRayCollisionTriangle" retType="RayCollision" paramCount="4" desc="Get collision info between ray and triangle">
            <Param type="Ray" name="ray" desc="" />
            <Param type="Vector3" name="p1" desc="" />
            <Param type="Vector3" name="p2" desc="" />
            <Param type="Vector3" name="p3" desc="" />
        </Function>
        <Function name="GetRayCollisionQuad" retType="RayCollision" paramCount="5" desc="Get collision info between ray and quad">
            <Param type="Ray" name="ray" desc="" />
            <Param type="Vector3" name="p1" desc="" />
            <Param type="Vector3" name="p2" desc="" />
            <Param type="Vector3" name="p3" desc="" />
            <Param type="Vector3" name="p4" desc="" />
        </Function>
        <Function name="InitAudioDevice" retType="void" paramCount="0" desc="Initialize audio device and context">
        </Function>
        <Function name="CloseAudioDevice" retType="void" paramCount="0" desc="Close the audio device and context">
        </Function>
        <Function name="IsAudioDeviceReady" retType="bool" paramCount="0" desc="Check if audio device has been initialized successfully">
        </Function>
        <Function name="SetMasterVolume" retType="void" paramCount="1" desc="Set master volume (listener)">
            <Param type="float" name="volume" desc="" />
        </Function>
        <Function name="LoadWave" retType="Wave" paramCount="1" desc="Load wave data from file">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadWaveFromMemory" retType="Wave" paramCount="3" desc="Load wave from memory buffer, fileType refers to extension: i.e. '.wav'">
            <Param type="const char *" name="fileType" desc="" />
            <Param type="const unsigned char *" name="fileData" desc="" />
            <Param type="int" name="dataSize" desc="" />
        </Function>
        <Function name="IsWaveReady" retType="bool" paramCount="1" desc="Checks if wave data is ready">
            <Param type="Wave" name="wave" desc="" />
        </Function>
        <Function name="LoadSound" retType="Sound" paramCount="1" desc="Load sound from file">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadSoundFromWave" retType="Sound" paramCount="1" desc="Load sound from wave data">
            <Param type="Wave" name="wave" desc="" />
        </Function>
        <Function name="IsSoundReady" retType="bool" paramCount="1" desc="Checks if a sound is ready">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="UpdateSound" retType="void" paramCount="3" desc="Update sound buffer with new data">
            <Param type="Sound" name="sound" desc="" />
            <Param type="const void *" name="data" desc="" />
            <Param type="int" name="sampleCount" desc="" />
        </Function>
        <Function name="UnloadWave" retType="void" paramCount="1" desc="Unload wave data">
            <Param type="Wave" name="wave" desc="" />
        </Function>
        <Function name="UnloadSound" retType="void" paramCount="1" desc="Unload sound">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="ExportWave" retType="bool" paramCount="2" desc="Export wave data to file, returns true on success">
            <Param type="Wave" name="wave" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="ExportWaveAsCode" retType="bool" paramCount="2" desc="Export wave sample data to code (.h), returns true on success">
            <Param type="Wave" name="wave" desc="" />
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="PlaySound" retType="void" paramCount="1" desc="Play a sound">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="StopSound" retType="void" paramCount="1" desc="Stop playing a sound">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="PauseSound" retType="void" paramCount="1" desc="Pause a sound">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="ResumeSound" retType="void" paramCount="1" desc="Resume a paused sound">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="IsSoundPlaying" retType="bool" paramCount="1" desc="Check if a sound is currently playing">
            <Param type="Sound" name="sound" desc="" />
        </Function>
        <Function name="SetSoundVolume" retType="void" paramCount="2" desc="Set volume for a sound (1.0 is max level)">
            <Param type="Sound" name="sound" desc="" />
            <Param type="float" name="volume" desc="" />
        </Function>
        <Function name="SetSoundPitch" retType="void" paramCount="2" desc="Set pitch for a sound (1.0 is base level)">
            <Param type="Sound" name="sound" desc="" />
            <Param type="float" name="pitch" desc="" />
        </Function>
        <Function name="SetSoundPan" retType="void" paramCount="2" desc="Set pan for a sound (0.5 is center)">
            <Param type="Sound" name="sound" desc="" />
            <Param type="float" name="pan" desc="" />
        </Function>
        <Function name="WaveCopy" retType="Wave" paramCount="1" desc="Copy a wave to a new wave">
            <Param type="Wave" name="wave" desc="" />
        </Function>
        <Function name="WaveCrop" retType="void" paramCount="3" desc="Crop a wave to defined samples range">
            <Param type="Wave *" name="wave" desc="" />
            <Param type="int" name="initSample" desc="" />
            <Param type="int" name="finalSample" desc="" />
        </Function>
        <Function name="WaveFormat" retType="void" paramCount="4" desc="Convert wave data to desired format">
            <Param type="Wave *" name="wave" desc="" />
            <Param type="int" name="sampleRate" desc="" />
            <Param type="int" name="sampleSize" desc="" />
            <Param type="int" name="channels" desc="" />
        </Function>
        <Function name="LoadWaveSamples" retType="float *" paramCount="1" desc="Load samples data from wave as a 32bit float data array">
            <Param type="Wave" name="wave" desc="" />
        </Function>
        <Function name="UnloadWaveSamples" retType="void" paramCount="1" desc="Unload samples data loaded with LoadWaveSamples()">
            <Param type="float *" name="samples" desc="" />
        </Function>
        <Function name="LoadMusicStream" retType="Music" paramCount="1" desc="Load music stream from file">
            <Param type="const char *" name="fileName" desc="" />
        </Function>
        <Function name="LoadMusicStreamFromMemory" retType="Music" paramCount="3" desc="Load music stream from data">
            <Param type="const char *" name="fileType" desc="" />
            <Param type="const unsigned char *" name="data" desc="" />
            <Param type="int" name="dataSize" desc="" />
        </Function>
        <Function name="IsMusicReady" retType="bool" paramCount="1" desc="Checks if a music stream is ready">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="UnloadMusicStream" retType="void" paramCount="1" desc="Unload music stream">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="PlayMusicStream" retType="void" paramCount="1" desc="Start music playing">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="IsMusicStreamPlaying" retType="bool" paramCount="1" desc="Check if music is playing">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="UpdateMusicStream" retType="void" paramCount="1" desc="Updates buffers for music streaming">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="StopMusicStream" retType="void" paramCount="1" desc="Stop music playing">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="PauseMusicStream" retType="void" paramCount="1" desc="Pause music playing">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="ResumeMusicStream" retType="void" paramCount="1" desc="Resume playing paused music">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="SeekMusicStream" retType="void" paramCount="2" desc="Seek music to a position (in seconds)">
            <Param type="Music" name="music" desc="" />
            <Param type="float" name="position" desc="" />
        </Function>
        <Function name="SetMusicVolume" retType="void" paramCount="2" desc="Set volume for music (1.0 is max level)">
            <Param type="Music" name="music" desc="" />
            <Param type="float" name="volume" desc="" />
        </Function>
        <Function name="SetMusicPitch" retType="void" paramCount="2" desc="Set pitch for a music (1.0 is base level)">
            <Param type="Music" name="music" desc="" />
            <Param type="float" name="pitch" desc="" />
        </Function>
        <Function name="SetMusicPan" retType="void" paramCount="2" desc="Set pan for a music (0.5 is center)">
            <Param type="Music" name="music" desc="" />
            <Param type="float" name="pan" desc="" />
        </Function>
        <Function name="GetMusicTimeLength" retType="float" paramCount="1" desc="Get music time length (in seconds)">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="GetMusicTimePlayed" retType="float" paramCount="1" desc="Get current music time played (in seconds)">
            <Param type="Music" name="music" desc="" />
        </Function>
        <Function name="LoadAudioStream" retType="AudioStream" paramCount="3" desc="Load audio stream (to stream raw audio pcm data)">
            <Param type="unsigned int" name="sampleRate" desc="" />
            <Param type="unsigned int" name="sampleSize" desc="" />
            <Param type="unsigned int" name="channels" desc="" />
        </Function>
        <Function name="IsAudioStreamReady" retType="bool" paramCount="1" desc="Checks if an audio stream is ready">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="UnloadAudioStream" retType="void" paramCount="1" desc="Unload audio stream and free memory">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="UpdateAudioStream" retType="void" paramCount="3" desc="Update audio stream buffers with data">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="const void *" name="data" desc="" />
            <Param type="int" name="frameCount" desc="" />
        </Function>
        <Function name="IsAudioStreamProcessed" retType="bool" paramCount="1" desc="Check if any audio stream buffers requires refill">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="PlayAudioStream" retType="void" paramCount="1" desc="Play audio stream">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="PauseAudioStream" retType="void" paramCount="1" desc="Pause audio stream">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="ResumeAudioStream" retType="void" paramCount="1" desc="Resume audio stream">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="IsAudioStreamPlaying" retType="bool" paramCount="1" desc="Check if audio stream is playing">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="StopAudioStream" retType="void" paramCount="1" desc="Stop audio stream">
            <Param type="AudioStream" name="stream" desc="" />
        </Function>
        <Function name="SetAudioStreamVolume" retType="void" paramCount="2" desc="Set volume for audio stream (1.0 is max level)">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="float" name="volume" desc="" />
        </Function>
        <Function name="SetAudioStreamPitch" retType="void" paramCount="2" desc="Set pitch for audio stream (1.0 is base level)">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="float" name="pitch" desc="" />
        </Function>
        <Function name="SetAudioStreamPan" retType="void" paramCount="2" desc="Set pan for audio stream (0.5 is centered)">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="float" name="pan" desc="" />
        </Function>
        <Function name="SetAudioStreamBufferSizeDefault" retType="void" paramCount="1" desc="Default size for new audio streams">
            <Param type="int" name="size" desc="" />
        </Function>
        <Function name="SetAudioStreamCallback" retType="void" paramCount="2" desc="Audio thread callback to request new data">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="AudioCallback" name="callback" desc="" />
        </Function>
        <Function name="AttachAudioStreamProcessor" retType="void" paramCount="2" desc="Attach audio stream processor to stream">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="AudioCallback" name="processor" desc="" />
        </Function>
        <Function name="DetachAudioStreamProcessor" retType="void" paramCount="2" desc="Detach audio stream processor from stream">
            <Param type="AudioStream" name="stream" desc="" />
            <Param type="AudioCallback" name="processor" desc="" />
        </Function>
        <Function name="AttachAudioMixedProcessor" retType="void" paramCount="1" desc="Attach audio stream processor to the entire audio pipeline">
            <Param type="AudioCallback" name="processor" desc="" />
        </Function>
        <Function name="DetachAudioMixedProcessor" retType="void" paramCount="1" desc="Detach audio stream processor from the entire audio pipeline">
            <Param type="AudioCallback" name="processor" desc="" />
        </Function>
    </Functions>
</raylibAPI>
