## Contributing to raylib

Hello contributors! Welcome to raylib! 

Do you enjoy raylib and want to contribute? Nice! You can help with the following points:

- `C programming` - Can you write/review/test/improve the code? 
- `Documentation/Tutorials/Example` - Can you write some tutorial/example?
- `Porting to other platforms` - Can you port/adapt/compile raylib on other systems?
- `Web Development` - Can you help [with the website](https://github.com/raysan5/raylib.com)?
- `Testing` - Can you find some bugs in raylib?

This document contains a set of guidelines to contribute to the project. These are mostly guidelines, not rules. 
Use your best judgement, and feel free to propose changes to this document in a pull request.

### raylib philosophy

 - raylib is a tool to **ENJOY** videogames programming, every function in raylib is designed as a mini-tutorial on itself.
 - raylib is **SIMPLE** and **EASY-TO-USE**, I tried to keep it compact with a small set of functions, if a function is too complex, better not including it.
 - raylib is open source and free; educators and institutions can use this tool to **TEACH** videogames programming completely for free.
 - raylib is collaborative; contribution of tutorials / code examples / bug fixes / code comments are highly appreciated.
 - raylib's license (and its external libs respective licenses) allow using raylib on commercial projects.

### Some interesting reads to start with

 - [raylib history](HISTORY.md)
 - [raylib architecture](https://github.com/raysan5/raylib/wiki/raylib-architecture)
 - [raylib license](LICENSE)
 - [raylib roadmap](ROADMAP.md)
 
[raylib Wiki](https://github.com/raysan5/raylib/wiki) contains some information about the library and is open to anyone for edit. 
Feel free to review it if required, just take care not to break something.

### raylib C coding conventions

Despite being written in C, raylib does not follow the standard Hungarian notation for C, 
it [follows Pascal-case/camel-case notation](https://github.com/raysan5/raylib/wiki/raylib-coding-conventions), 
more common on C# language. All code formatting decisions have been carefully taken
to make it easier for students/users to read, write and understand code.

Source code is extensively commented for that purpose, raylib primary learning method is:

 > `Learn by reading code and examples`

For detailed information on building raylib and examples, please check [raylib Wiki](https://github.com/raysan5/raylib/wiki).

### Opening new Issues

To open new issue for raylib (bug, enhancement, discussion...), just try to follow these rules:

 - Make sure the issue has not already been reported before by searching on GitHub under Issues.
 - If you're unable to find an open issue addressing the problem, open a new one. Be sure to include a 
 title and clear description, as much relevant information as possible, and a code sample demonstrating the unexpected behavior.
 - If applies, attach some screenshot of the issue and a .zip file with the code sample and required resources.
 - On issue description, add a brackets tag about the raylib module that relates to this issue. 
 If don't know which module, just report the issue, I will review it.
 - You can check other issues to see how is being done!

### Sending a Pull-Request

 - Make sure the PR description clearly describes the problem and solution. Include the relevant issue number if applicable.
 - Don't send big pull requests (lots of changelists), they are difficult to review. It's better to send small pull requests, one at a time.
 - Verify that changes don't break the build (at least on Windows platform). As many platforms where you can test it, the better, but don't worry
 if you cannot test all the platforms.

### Contact information

If you have any doubt, don't hesitate to [contact me](mailto:<EMAIL>)!.
You can write me a direct mail but you can also contact me on the following networks:

 - [raylib Discord](https://discord.gg/raylib) - A direct communication channel for project discussions.
 - [raylib twitter](https://twitter.com/raysan5) - My personal twitter account, I usually post about raylib, you can send me PMs.
 - [raylib reddit](https://www.reddit.com/r/raylib/) - A good place for discussions or to ask for help.
 - [raylib web](http://www.raylib.com/) - On top-right corner there is a bunch of networks where you can find me.

Thank you very much for your time! :)
