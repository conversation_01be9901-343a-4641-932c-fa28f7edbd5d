#**************************************************************************************************
#
#   raylib makefile for Desktop platforms, Raspberry Pi, Android and HTML5
#
#   Copyright (c) 2013-2023 Ramon <PERSON> (@raysan5)
#
#   This software is provided "as-is", without any express or implied warranty. In no event
#   will the authors be held liable for any damages arising from the use of this software.
#
#   Permission is granted to anyone to use this software for any purpose, including commercial
#   applications, and to alter it and redistribute it freely, subject to the following restrictions:
#
#     1. The origin of this software must not be misrepresented; you must not claim that you
#     wrote the original software. If you use this software in a product, an acknowledgment
#     in the product documentation would be appreciated but is not required.
#
#     2. Altered source versions must be plainly marked as such, and must not be misrepresented
#     as being the original software.
#
#     3. This notice may not be removed or altered from any source distribution.
#
#**************************************************************************************************

.PHONY: all clean

# Define required environment variables
#------------------------------------------------------------------------------------------------
# Define target platform: PLATFORM_DESKTOP, PLATFORM_RPI, PLATFORM_DRM, PLATFORM_ANDROID, PLATFORM_WEB
PLATFORM              ?= PLATFORM_WEB

# Define required raylib variables
PROJECT_NAME          ?= raylib_examples
RAYLIB_VERSION        ?= 4.5.0
RAYLIB_PATH           ?= ..

# Locations of raylib.h and libraylib.a/libraylib.so
# NOTE: Those variables are only used for PLATFORM_OS: LINUX, BSD
RAYLIB_INCLUDE_PATH   ?= /usr/local/include
RAYLIB_LIB_PATH       ?= /usr/local/lib

# Library type compilation: STATIC (.a) or SHARED (.so/.dll)
RAYLIB_LIBTYPE        ?= STATIC

# Build mode for project: DEBUG or RELEASE
BUILD_MODE            ?= RELEASE

# Use external GLFW library instead of rglfw module
USE_EXTERNAL_GLFW     ?= FALSE

# Use Wayland display server protocol on Linux desktop (by default it uses X11 windowing system)
# NOTE: This variable is only used for PLATFORM_OS: LINUX
USE_WAYLAND_DISPLAY   ?= FALSE

# Use cross-compiler for PLATFORM_RPI
ifeq ($(PLATFORM),PLATFORM_RPI)
    USE_RPI_CROSS_COMPILER ?= FALSE
    ifeq ($(USE_RPI_CROSS_COMPILER),TRUE)
        RPI_TOOLCHAIN ?= C:/SysGCC/Raspberry
        RPI_TOOLCHAIN_SYSROOT ?= $(RPI_TOOLCHAIN)/arm-linux-gnueabihf/sysroot
    endif
endif

# Determine PLATFORM_OS in case PLATFORM_DESKTOP or PLATFORM_WEB selected
ifeq ($(PLATFORM),$(filter $(PLATFORM),PLATFORM_DESKTOP PLATFORM_WEB))
    # No uname.exe on MinGW!, but OS=Windows_NT on Windows!
    # ifeq ($(UNAME),Msys) -> Windows
    ifeq ($(OS),Windows_NT)
        PLATFORM_OS = WINDOWS
    else
        UNAMEOS = $(shell uname)
        ifeq ($(UNAMEOS),Linux)
            PLATFORM_OS = LINUX
        endif
        ifeq ($(UNAMEOS),FreeBSD)
            PLATFORM_OS = BSD
        endif
        ifeq ($(UNAMEOS),OpenBSD)
            PLATFORM_OS = BSD
        endif
        ifeq ($(UNAMEOS),NetBSD)
            PLATFORM_OS = BSD
        endif
        ifeq ($(UNAMEOS),DragonFly)
            PLATFORM_OS = BSD
        endif
        ifeq ($(UNAMEOS),Darwin)
            PLATFORM_OS = OSX
        endif
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    UNAMEOS = $(shell uname)
    ifeq ($(UNAMEOS),Linux)
        PLATFORM_OS = LINUX
    endif
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
    UNAMEOS = $(shell uname)
    ifeq ($(UNAMEOS),Linux)
        PLATFORM_OS = LINUX
    endif
endif

# RAYLIB_PATH adjustment for LINUX platform
# TODO: Do we really need this?
ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),LINUX)
        RAYLIB_PREFIX  ?= ..
        RAYLIB_PATH     = $(realpath $(RAYLIB_PREFIX))
    endif
endif

# Default path for raylib on Raspberry Pi
ifeq ($(PLATFORM),PLATFORM_RPI)
    RAYLIB_PATH        ?= /home/<USER>/raylib
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
    RAYLIB_PATH        ?= /home/<USER>/raylib
endif

# Define raylib release directory for compiled library
RAYLIB_RELEASE_PATH    ?= $(RAYLIB_PATH)/src

ifeq ($(PLATFORM),PLATFORM_WEB)
    ifeq ($(PLATFORM_OS),WINDOWS)
        # Emscripten required variables
		EMSDK_PATH         ?= C:/emsdk
		EMSCRIPTEN_PATH    ?= $(EMSDK_PATH)/upstream/emscripten
		CLANG_PATH          = $(EMSDK_PATH)/upstream/bin
		PYTHON_PATH         = $(EMSDK_PATH)/python/3.9.2-1_64bit
		NODE_PATH           = $(EMSDK_PATH)/node/14.15.5_64bit/bin
		export PATH         = $(EMSDK_PATH);$(EMSCRIPTEN_PATH);$(CLANG_PATH);$(NODE_PATH);$(PYTHON_PATH):$$(PATH)
    endif
endif

# Define default C compiler: CC
#------------------------------------------------------------------------------------------------
CC = gcc

ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),OSX)
        # OSX default compiler
        CC = clang
    endif
    ifeq ($(PLATFORM_OS),BSD)
        # FreeBSD, OpenBSD, NetBSD, DragonFly default compiler
        CC = clang
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    ifeq ($(USE_RPI_CROSS_COMPILER),TRUE)
        # Define RPI cross-compiler
        #CC = armv6j-hardfloat-linux-gnueabi-gcc
        CC = $(RPI_TOOLCHAIN)/bin/arm-linux-gnueabihf-gcc
    endif
endif
ifeq ($(PLATFORM),PLATFORM_WEB)
    # HTML5 emscripten compiler
    # WARNING: To compile to HTML5, code must be redesigned
    # to use emscripten.h and emscripten_set_main_loop()
    CC = emcc
endif

# Define default make program: MAKE
#------------------------------------------------------------------------------------------------
MAKE ?= make

ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),WINDOWS)
        MAKE = mingw32-make
    endif
endif
ifeq ($(PLATFORM),PLATFORM_ANDROID)
    MAKE = mingw32-make
endif
ifeq ($(PLATFORM),PLATFORM_WEB)
    MAKE = mingw32-make
endif

# Define compiler flags: CFLAGS
#------------------------------------------------------------------------------------------------
#  -O1                  defines optimization level
#  -g                   include debug information on compilation
#  -s                   strip unnecessary data from build
#  -Wall                turns on most, but not all, compiler warnings
#  -std=c99             defines C language mode (standard C from 1999 revision)
#  -std=gnu99           defines C language mode (GNU C from 1999 revision)
#  -Wno-missing-braces  ignore invalid warning (GCC bug 53119)
#  -Wno-unused-value    ignore unused return values of some functions (i.e. fread())
#  -D_DEFAULT_SOURCE    use with -std=c99 on Linux and PLATFORM_WEB, required for timespec
CFLAGS = -Wall -std=c99 -D_DEFAULT_SOURCE -Wno-missing-braces -Wunused-result

ifeq ($(BUILD_MODE),DEBUG)
    CFLAGS += -g -D_DEBUG
    ifeq ($(PLATFORM),PLATFORM_WEB)
        CFLAGS += -s ASSERTIONS=1 --profiling
    endif
else
    ifeq ($(PLATFORM),PLATFORM_WEB)
        ifeq ($(BUILD_WEB_ASYNCIFY),TRUE)
            CFLAGS += -O3
        else
            CFLAGS += -Os
        endif
    else
        CFLAGS += -s -O2
    endif
endif

# Additional flags for compiler (if desired)
#  -Wextra                  enables some extra warning flags that are not enabled by -Wall
#  -Wmissing-prototypes     warn if a global function is defined without a previous prototype declaration
#  -Wstrict-prototypes      warn if a function is declared or defined without specifying the argument types
#  -Werror=implicit-function-declaration   catch function calls without prior declaration
#CFLAGS += -Wextra -Wmissing-prototypes -Wstrict-prototypes
ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),LINUX)
        ifeq ($(RAYLIB_LIBTYPE),STATIC)
            CFLAGS += -D_DEFAULT_SOURCE
        endif
        ifeq ($(RAYLIB_LIBTYPE),SHARED)
            # Explicitly enable runtime link to libraylib.so
            CFLAGS += -Wl,-rpath,$(RAYLIB_RELEASE_PATH)
        endif
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    CFLAGS += -std=gnu99
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
    CFLAGS += -std=gnu99 -DEGL_NO_X11
endif

# Define include paths for required headers: INCLUDE_PATHS
# NOTE: Some external/extras libraries could be required (stb, easings...)
#------------------------------------------------------------------------------------------------
INCLUDE_PATHS = -I. -I$(RAYLIB_PATH)/src -I$(RAYLIB_PATH)/src/external

# Define additional directories containing required header files
ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),BSD)
        INCLUDE_PATHS += -I$(RAYLIB_INCLUDE_PATH)
    endif
    ifeq ($(PLATFORM_OS),LINUX)
        INCLUDE_PATHS += -I$(RAYLIB_INCLUDE_PATH)
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    INCLUDE_PATHS += -I$(RPI_TOOLCHAIN_SYSROOT)/opt/vc/include
    INCLUDE_PATHS += -I$(RPI_TOOLCHAIN_SYSROOT)/opt/vc/include/interface/vmcs_host/linux
    INCLUDE_PATHS += -I$(RPI_TOOLCHAIN_SYSROOT)/opt/vc/include/interface/vcos/pthreads
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
    INCLUDE_PATHS += -I/usr/include/libdrm
endif

# Define library paths containing required libs: LDFLAGS
#------------------------------------------------------------------------------------------------
LDFLAGS = -L. -L$(RAYLIB_RELEASE_PATH) -L$(RAYLIB_PATH)/src

ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),WINDOWS)
        # NOTE: The resource .rc file contains windows executable icon and properties
        LDFLAGS += $(RAYLIB_PATH)/src/raylib.rc.data
        # -Wl,--subsystem,windows hides the console window
        ifeq ($(BUILD_MODE), RELEASE)
            LDFLAGS += -Wl,--subsystem,windows
        endif
    endif
    ifeq ($(PLATFORM_OS),LINUX)
        LDFLAGS += -L$(RAYLIB_LIB_PATH)
    endif
    ifeq ($(PLATFORM_OS),BSD)
        LDFLAGS += -Lsrc -L$(RAYLIB_LIB_PATH)
    endif
endif
ifeq ($(PLATFORM),PLATFORM_WEB)
    # -Os                        # size optimization
    # -O2                        # optimization level 2, if used, also set --memory-init-file 0
    # -s USE_GLFW=3              # Use glfw3 library (context/input management)
    # -s ALLOW_MEMORY_GROWTH=1   # to allow memory resizing -> WARNING: Audio buffers could FAIL!
    # -s TOTAL_MEMORY=16777216   # to specify heap memory size (default = 16MB) (67108864 = 64MB)
    # -s USE_PTHREADS=1          # multithreading support
    # -s WASM=0                  # disable Web Assembly, emitted by default
    # -s ASYNCIFY                # lets synchronous C/C++ code interact with asynchronous JS
    # -s FORCE_FILESYSTEM=1      # force filesystem to load/save files data
    # -s ASSERTIONS=1            # enable runtime checks for common memory allocation errors (-O1 and above turn it off)
    # -s EXPORTED_RUNTIME_METHODS=ccall  # require exporting some LEGACY_RUNTIME functions, ccall() is required by miniaudio
    # --profiling                # include information for code profiling
    # --memory-init-file 0       # to avoid an external memory initialization code file (.mem)
    # --preload-file resources   # specify a resources folder for data compilation
    # --source-map-base          # allow debugging in browser with source map
    LDFLAGS += -s USE_GLFW=3 -s ASYNCIFY -s EXPORTED_RUNTIME_METHODS=ccall

    # NOTE: Simple raylib examples are compiled to be interpreter with asyncify, that way,
    # we can compile same code for ALL platforms with no change required, but, working on bigger
    # projects, code needs to be refactored to avoid a blocking while() loop, moving Update and Draw
    # logic to a self contained function: UpdateDrawFrame(), check core_basic_window_web.c for reference.

    # NOTE: Additional compilate flags for TOTAL_MEMORY, FORCE_FILESYSTEM and resources loading
    # are specified per-example for optimization

    # Define a custom shell .html and output extension
    LDFLAGS += --shell-file $(RAYLIB_PATH)/src/shell.html
    EXT = .html
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    LDFLAGS += -L$(RPI_TOOLCHAIN_SYSROOT)/opt/vc/lib
endif

# Define libraries required on linking: LDLIBS
# NOTE: To link libraries (lib<name>.so or lib<name>.a), use -l<name>
#------------------------------------------------------------------------------------------------
ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),WINDOWS)
        # Libraries for Windows desktop compilation
        # NOTE: WinMM library required to set high-res timer resolution
        LDLIBS = -lraylib -lopengl32 -lgdi32 -lwinmm
    endif
    ifeq ($(PLATFORM_OS),LINUX)
        # Libraries for Debian GNU/Linux desktop compiling
        # NOTE: Required packages: libegl1-mesa-dev
        LDLIBS = -lraylib -lGL -lm -lpthread -ldl -lrt

        # On X11 requires also below libraries
        LDLIBS += -lX11
        # NOTE: It seems additional libraries are not required any more, latest GLFW just dlopen them
        #LDLIBS += -lXrandr -lXinerama -lXi -lXxf86vm -lXcursor

        # On Wayland windowing system, additional libraries requires
        ifeq ($(USE_WAYLAND_DISPLAY),TRUE)
            LDLIBS += -lwayland-client -lwayland-cursor -lwayland-egl -lxkbcommon
        endif
        # Explicit link to libc
        ifeq ($(RAYLIB_LIBTYPE),SHARED)
            LDLIBS += -lc
        endif

        # NOTE: On ARM 32bit arch, miniaudio requires atomics library
        LDLIBS += -latomic
    endif
    ifeq ($(PLATFORM_OS),OSX)
        # Libraries for OSX 10.9 desktop compiling
        # NOTE: Required packages: libopenal-dev libegl1-mesa-dev
        LDLIBS = -lraylib -framework OpenGL -framework Cocoa -framework IOKit -framework CoreAudio -framework CoreVideo
    endif
    ifeq ($(PLATFORM_OS),BSD)
        # Libraries for FreeBSD, OpenBSD, NetBSD, DragonFly desktop compiling
        # NOTE: Required packages: mesa-libs
        LDLIBS = -lraylib -lGL -lpthread -lm

        # On XWindow requires also below libraries
        LDLIBS += -lX11 -lXrandr -lXinerama -lXi -lXxf86vm -lXcursor
    endif
    ifeq ($(USE_EXTERNAL_GLFW),TRUE)
        # NOTE: It could require additional packages installed: libglfw3-dev
        LDLIBS += -lglfw
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
    # Libraries for Raspberry Pi compiling
    # NOTE: Required packages: libasound2-dev (ALSA)
    LDLIBS = -lraylib -lbrcmGLESv2 -lbrcmEGL -lpthread -lrt -lm -lbcm_host -ldl -latomic
    ifeq ($(USE_RPI_CROSS_COMPILER),TRUE)
        LDLIBS += -lvchiq_arm -lvcos
    endif
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
    # Libraries for DRM compiling
    # NOTE: Required packages: libasound2-dev (ALSA)
    LDLIBS = -lraylib -lGLESv2 -lEGL -lpthread -lrt -lm -lgbm -ldrm -ldl -latomic
endif
ifeq ($(PLATFORM),PLATFORM_WEB)
    # Libraries for web (HTML5) compiling
    LDLIBS = $(RAYLIB_RELEASE_PATH)/libraylib.a
endif

# Define source code object files required
#------------------------------------------------------------------------------------------------
CORE = \
    core/core_basic_window \
    core/core_basic_screen_manager \
    core/core_input_keys \
    core/core_input_mouse \
    core/core_input_mouse_wheel \
    core/core_input_gamepad \
    core/core_input_multitouch \
    core/core_input_gestures \
    core/core_2d_camera \
    core/core_2d_camera_platformer \
    core/core_2d_camera_mouse_zoom \
    core/core_3d_camera_mode \
    core/core_3d_camera_free \
    core/core_3d_camera_first_person \
    core/core_3d_picking \
    core/core_world_screen \
    core/core_custom_logging \
    core/core_drop_files \
    core/core_random_values \
    core/core_scissor_test \
    core/core_storage_values \
    core/core_vr_simulator \
    core/core_window_flags \
    core/core_window_letterbox \
    core/core_window_should_close \
    core/core_split_screen \
    core/core_smooth_pixelperfect \
    core/core_custom_frame_control \
    core/core_loading_thread

SHAPES = \
    shapes/shapes_basic_shapes \
    shapes/shapes_bouncing_ball \
    shapes/shapes_colors_palette \
    shapes/shapes_logo_raylib \
    shapes/shapes_logo_raylib_anim \
    shapes/shapes_rectangle_scaling \
    shapes/shapes_lines_bezier \
    shapes/shapes_collision_area \
    shapes/shapes_following_eyes \
    shapes/shapes_easings_ball_anim \
    shapes/shapes_easings_box_anim \
    shapes/shapes_easings_rectangle_array \
    shapes/shapes_draw_ring \
    shapes/shapes_draw_circle_sector \
    shapes/shapes_draw_rectangle_rounded \
    shapes/shapes_top_down_lights

TEXTURES = \
    textures/textures_logo_raylib \
    textures/textures_mouse_painting \
    textures/textures_srcrec_dstrec \
    textures/textures_image_drawing \
    textures/textures_image_generation \
    textures/textures_image_loading \
    textures/textures_image_processing \
    textures/textures_image_text \
    textures/textures_to_image \
    textures/textures_raw_data \
    textures/textures_particles_blending \
    textures/textures_npatch_drawing \
    textures/textures_background_scrolling \
    textures/textures_sprite_anim \
    textures/textures_sprite_button \
    textures/textures_sprite_explosion \
    textures/textures_textured_curve \
    textures/textures_bunnymark \
    textures/textures_blend_modes \
    textures/textures_draw_tiled \
    textures/textures_polygon \
    textures/textures_gif_player \
    textures/textures_fog_of_war

TEXT = \
    text/text_raylib_fonts \
    text/text_font_spritefont \
    text/text_font_loading \
    text/text_font_filters \
    text/text_font_sdf \
    text/text_format_text \
    text/text_input_box \
    text/text_writing_anim \
    text/text_rectangle_bounds \
    text/text_unicode \
    text/text_draw_3d \
    text/text_codepoints_loading

MODELS = \
    models/models_animation \
    models/models_billboard \
    models/models_box_collisions \
    models/models_cubicmap \
    models/models_draw_cube_texture \
    models/models_first_person_maze \
    models/models_geometric_shapes \
    models/models_mesh_generation \
    models/models_mesh_picking \
    models/models_loading \
    models/models_loading_vox \
    models/models_loading_gltf \
    models/models_loading_m3d \
    models/models_orthographic_projection \
    models/models_rlgl_solar_system \
    models/models_skybox \
    models/models_yaw_pitch_roll \
    models/models_heightmap \
    models/models_waving_cubes

SHADERS = \
    shaders/shaders_model_shader \
    shaders/shaders_shapes_textures \
    shaders/shaders_custom_uniform \
    shaders/shaders_postprocessing \
    shaders/shaders_palette_switch \
    shaders/shaders_raymarching \
    shaders/shaders_texture_drawing \
    shaders/shaders_texture_waves \
    shaders/shaders_texture_outline \
    shaders/shaders_julia_set \
    shaders/shaders_eratosthenes \
    shaders/shaders_basic_lighting \
    shaders/shaders_fog \
    shaders/shaders_simple_mask \
    shaders/shaders_spotlight \
    shaders/shaders_hot_reloading \
    shaders/shaders_mesh_instancing \
    shaders/shaders_multi_sample2d \
    shaders/shaders_write_depth \
    shaders/shaders_hybrid_render

AUDIO = \
    audio/audio_module_playing \
    audio/audio_music_stream \
    audio/audio_raw_stream \
    audio/audio_sound_loading \
    audio/audio_stream_effects \
    audio/audio_mixed_processor

CURRENT_MAKEFILE = $(lastword $(MAKEFILE_LIST))

# Define processes to execute
#------------------------------------------------------------------------------------------------
# Default target entry
all: $(CORE) $(SHAPES) $(TEXT) $(TEXTURES) $(MODELS) $(SHADERS) $(AUDIO)

core: $(CORE)
shapes: $(SHAPES)
textures: $(TEXTURES)
text: $(TEXT)
models: $(MODELS)
shaders: $(SHADERS)
audio: $(AUDIO)

# Compile CORE examples
core/core_basic_window: core/core_basic_window.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_basic_screen_manager: core/core_basic_screen_manager.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_input_keys: core/core_input_keys.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_input_mouse: core/core_input_mouse.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_input_mouse_wheel: core/core_input_mouse_wheel.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_input_gamepad: core/core_input_gamepad.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file core/resources/ps3.png@resources/ps3.png \
    --preload-file core/resources/xbox.png@resources/xbox.png

core/core_input_multitouch: core/core_input_multitouch.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_input_gestures: core/core_input_gestures.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_2d_camera: core/core_2d_camera.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_2d_camera_platformer: core/core_2d_camera_platformer.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)
    
core/core_2d_camera_mouse_zoom: core/core_2d_camera_mouse_zoom.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_3d_camera_mode: core/core_3d_camera_mode.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_3d_camera_free: core/core_3d_camera_free.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_3d_camera_first_person: core/core_3d_camera_first_person.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_3d_picking: core/core_3d_picking.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_world_screen: core/core_world_screen.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_custom_logging: core/core_custom_logging.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_window_letterbox: core/core_window_letterbox.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_drop_files: core/core_drop_files.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 -s FORCE_FILESYSTEM=1

core/core_random_values: core/core_random_values.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_scissor_test: core/core_scissor_test.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_storage_values: core/core_storage_values.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s FORCE_FILESYSTEM=1

core/core_vr_simulator: core/core_vr_simulator.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file core/resources/distortion100.fs@resources/distortion100.fs

core/core_window_flags: core/core_window_flags.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_split_screen: core/core_split_screen.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_smooth_pixelperfect: core/core_smooth_pixelperfect.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_custom_frame_control: core/core_custom_frame_control.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

core/core_window_should_close: core/core_window_should_close.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)
    
# NOTE: To use multi-threading raylib must be compiled with multi-theading support (-s USE_PTHREADS=1)
# WARNING: For security reasons multi-threading is not supported on browsers, it requires cross-origin isolation (Oct.2021)
# WARNING: It requires raylib to be compiled using -pthread, so atomic operations and thread-local data (if any) 
# in its source were transformed to non-atomic operations and non-thread-local data
core/core_loading_thread: core/core_loading_thread.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s USE_PTHREADS=1


# Compile SHAPES examples
shapes/shapes_basic_shapes: shapes/shapes_basic_shapes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_bouncing_ball: shapes/shapes_bouncing_ball.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_colors_palette: shapes/shapes_colors_palette.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_logo_raylib: shapes/shapes_logo_raylib.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_logo_raylib_anim: shapes/shapes_logo_raylib_anim.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_rectangle_scaling: shapes/shapes_rectangle_scaling.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_lines_bezier: shapes/shapes_lines_bezier.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_collision_area: shapes/shapes_collision_area.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_following_eyes: shapes/shapes_following_eyes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_easings_ball_anim: shapes/shapes_easings_ball_anim.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_easings_box_anim: shapes/shapes_easings_box_anim.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_easings_rectangle_array: shapes/shapes_easings_rectangle_array.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_draw_ring: shapes/shapes_draw_ring.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_draw_circle_sector: shapes/shapes_draw_circle_sector.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_draw_rectangle_rounded: shapes/shapes_draw_rectangle_rounded.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

shapes/shapes_top_down_lights: shapes/shapes_top_down_lights.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

# Compile TEXTURES examples
textures/textures_logo_raylib: textures/textures_logo_raylib.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/raylib_logo.png@resources/raylib_logo.png

textures/textures_mouse_painting: textures/textures_mouse_painting.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

textures/textures_sprite_anim: textures/textures_sprite_anim.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/scarfy.png@resources/scarfy.png

textures/textures_srcrec_dstrec: textures/textures_srcrec_dstrec.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/scarfy.png@resources/scarfy.png

textures/textures_image_loading: textures/textures_image_loading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/raylib_logo.png@resources/raylib_logo.png

textures/textures_image_drawing: textures/textures_image_drawing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/custom_jupiter_crash.png@resources/custom_jupiter_crash.png \
    --preload-file textures/resources/parrots.png@resources/parrots.png \
    --preload-file textures/resources/cat.png@resources/cat.png

textures/textures_image_generation: textures/textures_image_generation.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864

textures/textures_image_processing: textures/textures_image_processing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/parrots.png@resources/parrots.png

textures/textures_image_text: textures/textures_image_text.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file textures/resources/parrots.png@resources/parrots.png \
    --preload-file textures/resources/KAISG.ttf@resources/KAISG.ttf

textures/textures_to_image: textures/textures_to_image.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/raylib_logo.png@resources/raylib_logo.png

textures/textures_raw_data: textures/textures_raw_data.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/fudesumi.raw@resources/fudesumi.raw

textures/textures_particles_blending: textures/textures_particles_blending.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/spark_flame.png@resources/spark_flame.png

textures/textures_npatch_drawing: textures/textures_npatch_drawing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/ninepatch_button.png@resources/ninepatch_button.png

textures/textures_background_scrolling: textures/textures_background_scrolling.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/cyberpunk_street_background.png@resources/cyberpunk_street_background.png \
    --preload-file textures/resources/cyberpunk_street_midground.png@resources/cyberpunk_street_midground.png \
    --preload-file textures/resources/cyberpunk_street_foreground.png@resources/cyberpunk_street_foreground.png

textures/textures_sprite_button: textures/textures_sprite_button.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/button.png@resources/button.png \
    --preload-file textures/resources/buttonfx.wav@resources/buttonfx.wav

textures/textures_sprite_explosion: textures/textures_sprite_explosion.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/explosion.png@resources/explosion.png \
    --preload-file textures/resources/boom.wav@resources/boom.wav
    
textures/textures_textured_curve: textures/textures_textured_curve.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/road.png@resources/road.png

textures/textures_bunnymark: textures/textures_bunnymark.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/wabbit_alpha.png@resources/wabbit_alpha.png

textures/textures_blend_modes: textures/textures_blend_modes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/cyberpunk_street_background.png@resources/cyberpunk_street_background.png \
    --preload-file textures/resources/cyberpunk_street_foreground.png@resources/cyberpunk_street_foreground.png

textures/textures_draw_tiled: textures/textures_draw_tiled.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/patterns.png@resources/patterns.png

textures/textures_polygon: textures/textures_polygon.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/cat.png@resources/cat.png

textures/textures_gif_player: textures/textures_gif_player.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file textures/resources/scarfy_run.gif@resources/scarfy_run.gif

textures/textures_fog_of_war: textures/textures_fog_of_war.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

# Compile TEXT examples
text/text_raylib_fonts: text/text_raylib_fonts.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file text/resources/fonts/alagard.png@resources/fonts/alagard.png \
    --preload-file text/resources/fonts/pixelplay.png@resources/fonts/pixelplay.png \
    --preload-file text/resources/fonts/mecha.png@resources/fonts/mecha.png \
    --preload-file text/resources/fonts/setback.png@resources/fonts/setback.png \
    --preload-file text/resources/fonts/romulus.png@resources/fonts/romulus.png \
    --preload-file text/resources/fonts/pixantiqua.png@resources/fonts/pixantiqua.png \
    --preload-file text/resources/fonts/alpha_beta.png@resources/fonts/alpha_beta.png \
    --preload-file text/resources/fonts/jupiter_crash.png@resources/fonts/jupiter_crash.png

text/text_font_spritefont: text/text_font_spritefont.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file text/resources/custom_mecha.png@resources/custom_mecha.png \
    --preload-file text/resources/custom_alagard.png@resources/custom_alagard.png \
    --preload-file text/resources/custom_jupiter_crash.png@resources/custom_jupiter_crash.png

text/text_font_loading: text/text_font_loading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file text/resources/pixantiqua.fnt@resources/pixantiqua.fnt \
    --preload-file text/resources/pixantiqua.png@resources/pixantiqua.png \
    --preload-file text/resources/pixantiqua.ttf@resources/pixantiqua.ttf

text/text_font_filters: text/text_font_filters.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file text/resources/KAISG.ttf@resources/KAISG.ttf

text/text_font_sdf: text/text_font_sdf.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file text/resources/anonymous_pro_bold.ttf@resources/anonymous_pro_bold.ttf \
    --preload-file text/resources/shaders/glsl100/sdf.fs@resources/shaders/glsl100/sdf.fs

text/text_format_text: text/text_format_text.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

text/text_input_box: text/text_input_box.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

text/text_writing_anim: text/text_writing_anim.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

text/text_rectangle_bounds: text/text_rectangle_bounds.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

text/text_unicode: text/text_unicode.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file text/resources/dejavu.fnt@resources/dejavu.fnt \
    --preload-file text/resources/dejavu.png@resources/dejavu.png \
    --preload-file text/resources/noto_cjk.fnt@resources/noto_cjk.fnt \
    --preload-file text/resources/noto_cjk.png@resources/noto_cjk.png \
    --preload-file text/resources/symbola.fnt@resources/symbola.fnt \
    --preload-file text/resources/symbola.png@resources/symbola.png

text/text_draw_3d: text/text_draw_3d.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file text/resources/shaders/glsl100/alpha_discard.fs@resources/shaders/glsl100/alpha_discard.fs

text/text_codepoints_loading: text/text_codepoints_loading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file text/resources/DotGothic16-Regular.ttf@resources/DotGothic16-Regular.ttf

# Compile MODELS examples
models/models_animation: models/models_animation.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/iqm/guy.iqm@resources/models/iqm/guy.iqm \
    --preload-file models/resources/models/iqm/guytex.png@resources/models/iqm/guytex.png \
    --preload-file models/resources/models/iqm/guyanim.iqm@resources/models/iqm/guyanim.iqm

models/models_billboard: models/models_billboard.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/billboard.png@resources/billboard.png

models/models_box_collisions: models/models_box_collisions.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

models/models_cubicmap: models/models_cubicmap.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/cubicmap.png@resources/cubicmap.png \
    --preload-file models/resources/cubicmap_atlas.png@resources/cubicmap_atlas.png

models/models_draw_cube_texture: models/models_draw_cube_texture.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/cubicmap_atlas.png@resources/cubicmap_atlas.png    
    
models/models_first_person_maze: models/models_first_person_maze.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/cubicmap.png@resources/cubicmap.png \
    --preload-file models/resources/cubicmap_atlas.png@resources/cubicmap_atlas.png

models/models_geometric_shapes: models/models_geometric_shapes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

models/models_mesh_generation: models/models_mesh_generation.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

models/models_mesh_picking: models/models_mesh_picking.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/models/obj/turret.obj@resources/models/obj/turret.obj \
    --preload-file models/resources/models/obj/turret_diffuse.png@resources/models/obj/turret_diffuse.png

models/models_loading: models/models_loading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/obj/castle.obj@resources/models/obj/castle.obj \
    --preload-file models/resources/models/obj/castle_diffuse.png@resources/models/obj/castle_diffuse.png

models/models_loading_vox: models/models_loading_vox.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/vox/chr_knight.vox@resources/models/vox/chr_knight.vox \
    --preload-file models/resources/models/vox/chr_sword.vox@resources/models/vox/chr_sword.vox \
    --preload-file models/resources/models/vox/monu9.vox@resources/models/vox/monu9.vox

models/models_loading_gltf: models/models_loading_gltf.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/gltf/robot.glb@resources/models/gltf/robot.glb
    
models/models_loading_m3d: models/models_loading_m3d.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/m3d/cesium_man.m3d@resources/models/m3d/cesium_man.m3d

models/models_orthographic_projection: models/models_orthographic_projection.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

models/models_rlgl_solar_system: models/models_rlgl_solar_system.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

models/models_skybox: models/models_skybox.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 -s FORCE_FILESYSTEM=1 \
    --preload-file models/resources/dresden_square_2k.hdr@resources/dresden_square_2k.hdr \
    --preload-file models/resources/shaders/glsl100/skybox.vs@resources/shaders/glsl100/skybox.vs \
    --preload-file models/resources/shaders/glsl100/skybox.fs@resources/shaders/glsl100/skybox.fs \
    --preload-file models/resources/shaders/glsl100/cubemap.vs@resources/shaders/glsl100/cubemap.vs \
    --preload-file models/resources/shaders/glsl100/cubemap.fs@resources/shaders/glsl100/cubemap.fs

models/models_yaw_pitch_roll: models/models_yaw_pitch_roll.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file models/resources/models/obj/plane.obj@resources/models/obj/plane.obj \
    --preload-file models/resources/models/obj/plane_diffuse.png@resources/models/obj/plane_diffuse.png

models/models_heightmap: models/models_heightmap.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file models/resources/heightmap.png@resources/heightmap.png

models/models_waving_cubes: models/models_waving_cubes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM)

# Compile SHADER examples
shaders/shaders_model_shader: shaders/shaders_model_shader.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file shaders/resources/models/watermill.obj@resources/models/watermill.obj \
    --preload-file shaders/resources/models/watermill_diffuse.png@resources/models/watermill_diffuse.png \
    --preload-file shaders/resources/shaders/glsl100/grayscale.fs@resources/shaders/glsl100/grayscale.fs

shaders/shaders_shapes_textures: shaders/shaders_shapes_textures.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/fudesumi.png@resources/fudesumi.png \
    --preload-file shaders/resources/shaders/glsl100/base.vs@resources/shaders/glsl100/base.vs \
    --preload-file shaders/resources/shaders/glsl100/grayscale.fs@resources/shaders/glsl100/grayscale.fs

shaders/shaders_custom_uniform: shaders/shaders_custom_uniform.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file shaders/resources/models/barracks.obj@resources/models/barracks.obj \
    --preload-file shaders/resources/models/barracks_diffuse.png@resources/models/barracks_diffuse.png \
    --preload-file shaders/resources/shaders/glsl100/swirl.fs@resources/shaders/glsl100/swirl.fs

shaders/shaders_postprocessing: shaders/shaders_postprocessing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file shaders/resources/models/church.obj@resources/models/church.obj \
    --preload-file shaders/resources/models/church_diffuse.png@resources/models/church_diffuse.png \
    --preload-file shaders/resources/shaders/glsl100@resources/shaders/glsl100

shaders/shaders_palette_switch: shaders/shaders_palette_switch.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/palette_switch.fs@resources/shaders/glsl100/palette_switch.fs

shaders/shaders_raymarching: shaders/shaders_raymarching.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/raymarching.fs@resources/shaders/glsl100/raymarching.fs

shaders/shaders_texture_drawing: shaders/shaders_texture_drawing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/cubes_panning.fs@resources/shaders/glsl100/cubes_panning.fs

shaders/shaders_texture_waves: shaders/shaders_texture_waves.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/space.png@resources/space.png \
    --preload-file shaders/resources/shaders/glsl100/wave.fs@resources/shaders/glsl100/wave.fs

shaders/shaders_julia_set: shaders/shaders_julia_set.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/julia_set.fs@resources/shaders/glsl100/julia_set.fs

shaders/shaders_eratosthenes: shaders/shaders_eratosthenes.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/eratosthenes.fs@resources/shaders/glsl100/eratosthenes.fs

shaders/shaders_basic_lighting: shaders/shaders_basic_lighting.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/texel_checker.png@resources/texel_checker.png \
    --preload-file shaders/resources/shaders/glsl100/lighting.fs@resources/shaders/glsl100/lighting.fs \
    --preload-file shaders/resources/shaders/glsl100/lighting.vs@resources/shaders/glsl100/lighting.vs

shaders/shaders_fog: shaders/shaders_fog.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/texel_checker.png@resources/texel_checker.png \
    --preload-file shaders/resources/shaders/glsl100/fog.fs@resources/shaders/glsl100/fog.fs \
    --preload-file shaders/resources/shaders/glsl100/lighting.vs@resources/shaders/glsl100/lighting.vs

shaders/shaders_simple_mask: shaders/shaders_simple_mask.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/plasma.png@resources/plasma.png \
    --preload-file shaders/resources/mask.png@resources/mask.png \
    --preload-file shaders/resources/shaders/glsl100/mask.fs@resources/shaders/glsl100/mask.fs

shaders/shaders_spotlight: shaders/shaders_spotlight.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/raysan.png@resources/raysan.png \
    --preload-file shaders/resources/shaders/glsl100/spotlight.fs@resources/shaders/glsl100/spotlight.fs

shaders/shaders_hot_reloading: shaders/shaders_hot_reloading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s FORCE_FILESYSTEM=1 \
    --preload-file shaders/resources/shaders/glsl100/reload.fs@resources/shaders/glsl100/reload.fs

shaders/shaders_mesh_instancing: shaders/shaders_mesh_instancing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/lighting_instancing.vs@resources/shaders/glsl100/lighting_instancing.vs \
    --preload-file shaders/resources/shaders/glsl100/lighting.fs@resources/shaders/glsl100/lighting.fs

shaders/shaders_multi_sample2d: shaders/shaders_multi_sample2d.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/color_mix.fs@resources/shaders/glsl100/color_mix.fs

shaders/shaders_texture_outline: shaders/shaders_texture_outline.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/outline.fs@resources/shaders/glsl100/outline.fs \
    --preload-file shaders/resources/fudesumi.png@resources/fudesumi.png
    
shaders/shaders_write_depth: shaders/shaders_write_depth.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/write_depth.fs@resources/shaders/glsl100/write_depth.fs
    
shaders/shaders_hybrid_render: shaders/shaders_hybrid_render.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file shaders/resources/shaders/glsl100/hybrid_raymarch.fs@resources/shaders/glsl100/hybrid_raymarch.fs \
    --preload-file shaders/resources/shaders/glsl100/hybrid_raster.fs@resources/shaders/glsl100/hybrid_raster.fs
    
# Compile AUDIO examples
audio/audio_module_playing: audio/audio_module_playing.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file audio/resources/mini1111.xm@resources/mini1111.xm

audio/audio_music_stream: audio/audio_music_stream.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file audio/resources/country.mp3@resources/country.mp3

audio/audio_raw_stream: audio/audio_raw_stream.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864

audio/audio_sound_loading: audio/audio_sound_loading.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) \
    --preload-file audio/resources/sound.wav@resources/sound.wav \
    --preload-file audio/resources/target.ogg@resources/target.ogg

audio/audio_stream_effects: audio/audio_stream_effects.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file audio/resources/country.mp3@resources/country.mp3
    
audio/audio_mixed_processor: audio/audio_mixed_processor.c
	$(CC) -o $@$(EXT) $< $(CFLAGS) $(INCLUDE_PATHS) $(LDFLAGS) $(LDLIBS) -D$(PLATFORM) -s TOTAL_MEMORY=67108864 \
    --preload-file audio/resources/country.mp3@resources/country.mp3 \
    --preload-file audio/resources/coin.wav@resources/coin.wav

# Clean everything
clean:
ifeq ($(PLATFORM),PLATFORM_DESKTOP)
    ifeq ($(PLATFORM_OS),WINDOWS)
		del *.o *.exe /s
    endif
    ifeq ($(PLATFORM_OS),LINUX)
		find . -type f -executable -delete
		rm -fv *.o
    endif
    ifeq ($(PLATFORM_OS),OSX)
		find . -type f -perm +ugo+x -delete
		rm -f *.o
    endif
endif
ifeq ($(PLATFORM),PLATFORM_RPI)
	find . -type f -executable -delete
	rm -fv *.o
endif
ifeq ($(PLATFORM),PLATFORM_DRM)
	find . -type f -executable -delete
	rm -fv *.o
endif
ifeq ($(PLATFORM),PLATFORM_WEB)
	del *.o *.html *.js
endif
	@echo Cleaning done

