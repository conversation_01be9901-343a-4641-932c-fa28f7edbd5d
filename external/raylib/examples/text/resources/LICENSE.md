| resource                       | author      | licence | notes |
| :----------------------------- | :---------: | :------ | :---- |
| fonts/alagard.png              | He<PERSON><PERSON> Tsoi | [Freeware](https://www.dafont.com/es/alagard.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/romulus.png              | Hewett Tsoi | [Freeware](https://www.dafont.com/es/romulus.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/alpha_beta.png           | [<PERSON> (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/alpha-beta.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/setback.png              | [<PERSON> (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/setback.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/jupiter_crash.png        | [Brian Kent (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/jupiter-crash.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/mecha.png                | Captain Falcon | [Freeware](https://www.dafont.com/es/mecha-cf.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/pixelplay.png            | Aleksander Shevchuk | [Freeware](https://www.dafont.com/es/pixelplay.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| fonts/pixantiqua.ttf           | Gerhard Großmann | [Freeware](https://www.dafont.com/es/pixantiqua.font)  |  Atlas created by [@raysan5](https://github.com/raysan5) |
| anonymous_pro_bold.ttf         | [Mark Simonson](https://fonts.google.com/specimen/Anonymous+Pro) | [Open Font License](https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL) | - |
| custom_alagard.png             | [Brian Kent (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/jupiter-crash.font) | Atlas created by [@raysan5](https://github.com/raysan5) |
| custom_jupiter_crash.png       | [Brian Kent (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/jupiter-crash.font) | Atlas created by [@raysan5](https://github.com/raysan5) |
| custom_mecha.png               | [Brian Kent (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/jupiter-crash.font) | Atlas created by [@raysan5](https://github.com/raysan5) |
| dejavu.fnt, dejavu.png         | [DejaVu Fonts](https://dejavu-fonts.github.io/) | [Free](https://dejavu-fonts.github.io/License.html) | Atlas made with [BMFont](https://www.angelcode.com/products/bmfont/) by [@raysan5](https://github.com/raysan5) |
| KAISG.ttf                      | [Dieter Steffmann](http://www.steffmann.de/wordpress/) | [Freeware](https://www.1001fonts.com/users/steffmann/) | [Kaiserzeit Gotisch](https://www.dafont.com/es/kaiserzeit-gotisch.font) font |
| noto_cjk.fnt, noto_cjk.png     | [Google Fonts](https://www.google.com/get/noto/help/cjk/) |  [Open Font License](https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL) | Atlas made with [BMFont](https://www.angelcode.com/products/bmfont/) by [@raysan5](https://github.com/raysan5) |
| pixantiqua.fnt, pixantiqua.png | Gerhard Großmann | [Freeware](https://www.dafont.com/es/pixantiqua.font) | Atlas made with [BMFont](https://www.angelcode.com/products/bmfont/) by [@raysan5](https://github.com/raysan5) |
| pixantiqua.ttf                 | Gerhard Großmann | [Freeware](https://www.dafont.com/es/pixantiqua.font) | - |
| symbola.fnt, symbola.png       | George Douros | [Freeware](https://fontlibrary.org/en/font/symbola) | Atlas made with [BMFont](https://www.angelcode.com/products/bmfont/) by [@raysan5](https://github.com/raysan5) |
