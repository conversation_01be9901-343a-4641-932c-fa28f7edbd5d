| resource           | author        | licence | notes |
| :----------------- | :-----------: | :------ | :---- |
| models/barracks.obj,<br> models/barracks_diffuse.png | [<PERSON>](https://www.artstation.com/albertocano) | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/legalcode) | - |
| models/church.obj,<br> models/church_diffuse.png | [<PERSON>](https://www.artstation.com/albertocano) | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/legalcode)  | - |
| models/watermill.obj,<br> models/watermill_diffuse.png | [<PERSON>](https://www.artstation.com/albertocano) | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/legalcode)  | - |
| fudesumi.png       | [<PERSON><PERSON>](https://www.artstation.com/marshall_z)  | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/)  | - |
| mask.png           | [@raysan5](https://github.com/raysan5)      | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)   | Made with [EffectTextureMaker](https://mebiusbox.github.io/contents/EffectTextureMaker/) |
| plasma.png         | [@chriscamacho](https://github.com/chriscamacho)       | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)    | - |
| raysan.png         | [@raysan5](https://github.com/raysan5)      | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)   | - |
| space.png          | ❔             | ❔       | - |
| texel_checker.png  | [@raysan5](https://github.com/raysan5)      | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)   | Made with [UV Checker Map Maker](http://uvchecker.byvalle.com/) |
