| resource             | author      | licence | notes |
| :------------------- | :---------: | :------ | :---- |
| boom.wav             | [@raysan5](https://github.com/raysan5)    | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)     | Made with [rFXGen](https://raylibtech.itch.io/rfxgen) |
| buttonfx.wav         | [@raysan5](https://github.com/raysan5)    | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)     | Made with [rFXGen](https://raylibtech.itch.io/rfxgen) |
| button.png           | [@raysan5](https://github.com/raysan5)    | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)     | Made with [rFXGen](https://raylibtech.itch.io/rfxgen) |
| spark_flame.png      | [@raysan5](https://github.com/raysan5)    | [CC0](https://creativecommons.org/publicdomain/zero/1.0/)     | Made with [EffectTextureMaker](https://mebiusbox.github.io/contents/EffectTextureMaker/) |
| ninepatch_button.png | [@overdev](https://github.com/overdev)    | ❔     |
| explosion.png        | [Unity Labs Paris](https://blogs.unity3d.com/2016/11/28/free-vfx-image-sequences-flipbooks/) | [CC0](https://creativecommons.org/publicdomain/zero/1.0/) |
| parrots.png          | [Kodak set](http://r0k.us/graphics/kodak/)  | ❔       | Original name: `kodim23.png`
| cat.png              | ❔         | ❔     | - |
| wabbit_alpha.png     | ❔         | ❔     | - |
| custom_jupiter_crash.png | [Brian Kent (AEnigma)](https://www.dafont.com/es/aenigma.d188) | [Freeware](https://www.dafont.com/es/jupiter-crash.font) | Atlas created by [@raysan5](https://github.com/raysan5) |
| KAISG.ttf            | [Dieter Steffmann](http://www.steffmann.de/wordpress/) | [Freeware](https://www.1001fonts.com/users/steffmann/) | [Kaiserzeit Gotisch](https://www.dafont.com/es/kaiserzeit-gotisch.font) font |
| fudesumi.png         | [Eiden Marsal](https://www.artstation.com/marshall_z)  | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/)  | - |
| scarfy.png           | [Eiden Marsal](https://www.artstation.com/marshall_z) | [CC-BY-NC](https://creativecommons.org/licenses/by-nc/4.0/legalcode) | - |
| cyberpunk_street_background.png | [Luis Zuno](http://ansimuz.com/site/) | [CC-BY-3.0](http://creativecommons.org/licenses/by/3.0/) | [Cyberpunk Street Environment](https://ansimuz.itch.io/cyberpunk-street-environment) |
| cyberpunk_street_foreground.png | [Luis Zuno](http://ansimuz.com/site/) | [CC-BY-3.0](http://creativecommons.org/licenses/by/3.0/) | [Cyberpunk Street Environment](https://ansimuz.itch.io/cyberpunk-street-environment) |
| cyberpunk_street_midground.png  | [Luis Zuno](http://ansimuz.com/site/) | [CC-BY-3.0](http://creativecommons.org/licenses/by/3.0/) | [Cyberpunk Street Environment](https://ansimuz.itch.io/cyberpunk-street-environment) |
