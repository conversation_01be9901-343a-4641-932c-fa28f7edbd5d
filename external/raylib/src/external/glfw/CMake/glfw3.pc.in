prefix=@CMAKE_INSTALL_PREFIX@
exec_prefix=${prefix}
includedir=@CMAKE_INSTALL_FULL_INCLUDEDIR@
libdir=@CMAKE_INSTALL_FULL_LIBDIR@

Name: GLFW
Description: A multi-platform library for OpenGL, window and input
Version: @GLFW_VERSION@
URL: https://www.glfw.org/
Requires.private: @GLFW_PKG_CONFIG_REQUIRES_PRIVATE@
Libs: -L${libdir} -l@GLFW_LIB_NAME@
Libs.private: @GLFW_PKG_CONFIG_LIBS_PRIVATE@
Cflags: -I${includedir}
