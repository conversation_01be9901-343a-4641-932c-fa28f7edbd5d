# Find EpollShim
# Once done, this will define
#
#   EP<PERSON><PERSON>HIM_FOUND - System has EpollShim
#   EP<PERSON><PERSON>HIM_INCLUDE_DIRS - The EpollShim include directories
#   EPOLLSHIM_LIBRARIES - The libraries needed to use EpollShim

find_path(<PERSON><PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIRS NAMES sys/epoll.h sys/timerfd.h HINTS /usr/local/include/libepoll-shim)
find_library(EPOLLSHIM_LIBRARIES NAMES epoll-shim libepoll-shim HINTS /usr/local/lib)

if (EP<PERSON><PERSON>HIM_INCLUDE_DIRS AND EP<PERSON><PERSON>HIM_LIBRARIES)
	set(EP<PERSON><PERSON>HIM_FOUND TRUE)
endif (EP<PERSON><PERSON>HIM_INCLUDE_DIRS AND <PERSON><PERSON><PERSON><PERSON>IM_LIBRARIES)

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(EpollShim DEFAULT_MSG EPOLLSHIM_LIBRARIES EPOLLSHIM_INCLUDE_DIRS)
mark_as_advanced(EP<PERSON><PERSON>HIM_INCLUDE_DIRS EPOLLSHIM_LIBRARIES)
