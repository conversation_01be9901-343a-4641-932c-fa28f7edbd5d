# raylib bindings and wrappers

Some people ported raylib to other languages in form of bindings or wrappers to the library. Here is a list with all the ports available. Feel free to send a PR if you know of any binding/wrapper not in this list.

### Language Bindings

|  name              | raylib version  | language  | license    | repo                                                      |
|:------------------:|:---------------:|:---------:|:----------:|-----------------------------------------------------------|
| raylib             | **4.2** | [C/C++](https://en.wikipedia.org/wiki/C_(programming_language))    | Zlib | https://github.com/raysan5/raylib          |
| raylib-boo         | 3.7     | [Boo](http://boo-language.github.io/)| MIT | https://github.com/Rabios/raylib-boo          |
| Raylib-cs          | **4.2** | [C#](https://en.wikipedia.org/wiki/C_Sharp_(programming_language)) | Zlib | https://github.com/ChrisDill/Raylib-cs     |
| Raylib-CsLo        | **4.2** | [C#](https://en.wikipedia.org/wiki/C_Sharp_(programming_language)) | MPL-2.0 | https://github.com/NotNotTech/Raylib-CsLo  |
| cl-raylib          | **4.0** | [Common Lisp](https://common-lisp.net/)   | MIT | https://github.com/longlene/cl-raylib     |
| claylib/wrap       | **4.2** | [Common Lisp](https://common-lisp.net/)   | Zlib | https://github.com/defun-games/claylib |
| chez-raylib        | auto    | [Chez Scheme](https://cisco.github.io/ChezScheme/) | GPLv3 | https://github.com/Yunoinsky/chez-raylib |
| raylib-cr          | **4.5-dev (7e7939e)** | [Crystal](https://crystal-lang.org/)    | Apache-2.0 | https://github.com/sol-vin/raylib-cr |
| ray-cyber          | **4.2** | [Cyber](https://cyberscript.dev)  | MIT | https://github.com/fubark/ray-cyber |
| raylib-c3          | **4.5-dev** | [C3](https://c3-lang.org/)    | MIT | https://github.com/Its-Kenta/Raylib-C3 |
| dart-raylib        | **4.0** | [Dart](https://dart.dev/)               | MIT | https://gitlab.com/wolfenrain/dart-raylib   |
| bindbc-raylib3     | **4.0** | [D](https://dlang.org/)              | BSL-1.0 | https://github.com/o3o/bindbc-raylib3     |
| dray               | **4.2** | [D](https://dlang.org/)              | Apache-2.0 | https://github.com/redthing1/dray          |
| raylib-d           | **4.2** | [D](https://dlang.org/)              | Zlib | https://github.com/schveiguy/raylib-d        |
| dlang_raylib       | **4.0** | [D](https://dlang.org)                  | MPL-2.0 |https://github.com/rc-05/dlang_raylib          |
| rayex              | 3.7     | [elixir](https://elixir-lang.org/)      | Apache-2.0 | https://github.com/shiryel/rayex     |
| raylib-factor      | **4.0**     | [Factor](https://factorcode.org/)        | BSD  | https://github.com/factor/factor/blob/master/extra/raylib/raylib.factor   |
| raylib-freebasic   | **4.2** | [FreeBASIC](https://www.freebasic.net/) | MIT | https://github.com/WIITD/raylib-freebasic     |
| raylib-go          | **4.2** | [Go](https://golang.org/)            | Zlib | https://github.com/gen2brain/raylib-go       |
| raylib-guile       | auto    | [Guile](https://www.gnu.org/software/guile/) | Zlib | https://github.com/petelliott/raylib-guile |
| gforth-raylib      | 3.5     | [Gforth](https://gforth.org/)            | MIT  | https://github.com/ArnautDaniel/gforth-raylib   |
| h-raylib           | 4.5-dev | [Haskell](https://haskell.org/)          | Apache-2.0 | https://github.com/Anut-py/h-raylib       |
| raylib-hx          | **4.2**     | [Haxe](https://haxe.org/)                | Zlib | https://github.com/foreignsasquatch/raylib-hx    |
| hb-raylib          | 3.5     | [Harbour](https://harbour.github.io)                 | MIT | https://github.com/MarcosLeonardoMendezGerencir/hb-raylib |
| jaylib             | **4.2** | [Java](https://en.wikipedia.org/wiki/Java_(programming_language)) | GPLv3+CE | https://github.com/electronstudio/jaylib/  |
| raylib-j           | **4.0** | [Java](https://en.wikipedia.org/wiki/Java_(programming_language)) | Zlib | https://github.com/CreedVI/Raylib-J |
| raylib.jl          | **4.2** | [Julia](https://julialang.org/)      | Zlib | https://github.com/irishgreencitrus/raylib.jl |
| kaylib             | 3.7     | [Kotlin/native](https://kotlinlang.org) | ? | https://github.com/electronstudio/kaylib      |
| kaylib             | **4.5-dev**| [Kotlin/native](https://kotlinlang.org) | Zlib | https://codeberg.org/Kenta/Kaylib      |
| raylib-lua         | **4.2** | [Lua](http://www.lua.org/)           | ISC | https://github.com/TSnake41/raylib-lua        |
| raylua             | **4.0** | [Lua](http://www.lua.org/)           | MIT | https://github.com/Rabios/raylua              |
| nelua-raylib       | 4.0     | [nelua](https://nelua.io/)           | MIT | https://github.com/AKDev21/nelua-raylib       |
| Raylib-Nelua       | **4.5-dev**     | [nelua](https://nelua.io/)       | MIT | https://github.com/Its-Kenta/Raylib-Nelua     |
| NimraylibNow!      | 4.2     | [Nim](https://nim-lang.org/)         | MIT | https://github.com/greenfork/nimraylib_now    |
| raylib-Forever     | auto    | [Nim](https://nim-lang.org/)         | ? | https://github.com/Guevara-chan/Raylib-Forever  |
| naylib             | auto    | [Nim](https://nim-lang.org/)         | MIT | https://github.com/planetis-m/naylib          |
| node-raylib        | **4.0** | [Node.js](https://nodejs.org/en/)    | Zlib     | https://github.com/RobLoach/node-raylib         |
| raylib_odin_bindings | 4.0-dev | [Odin](https://odin-lang.org/)     | MIT | https://github.com/Deathbat2190/raylib_odin_bindings |
| raylib-odin        | **4.0** | [Odin](https://odin-lang.org/)       | BSD-3Clause | https://github.com/odin-lang/Odin/tree/master/vendor/raylib  |
| raylib-ocaml       | **4.2** | [OCaml](https://ocaml.org/)          | MIT | https://github.com/tjammer/raylib-ocaml        |
| TurboRaylib        | **4.2** | [Object Pascal](https://en.wikipedia.org/wiki/Object_Pascal) | MIT | https://github.com/turborium/TurboRaylib |
| Ray4Laz            | **4.2** | [Free Pascal](https://en.wikipedia.org/wiki/Free_Pascal)| Zlib |  https://github.com/GuvaCode/Ray4Laz |
| Raylib.4.0.Pascal  | **4.0** | [Free Pascal](https://en.wikipedia.org/wiki/Free_Pascal)| Zlib |  https://github.com/sysrpl/Raylib.4.0.Pascal |
| pyraylib           | 3.7     | [Python](https://www.python.org/)    | Zlib    | https://github.com/Ho011/pyraylib         |
| raylib-python-cffi | **4.2** | [Python](https://www.python.org/)    | EPL-2.0 | https://github.com/electronstudio/raylib-python-cffi |
| raylibpyctbg     | **4.2** | [Python](https://www.python.org/)          | MIT | https://github.com/overdev/raylibpyctbg                |
| raylib-py          | **4.2** | [Python](https://www.python.org/)          | MIT | https://github.com/overdev/raylib-py       |
| raylib-python-ctypes | **4.2** | [Python](https://www.python.org/)    | MIT | https://github.com/sDos280/raylib-python-ctypes |
| raylib-php         | 3.5     | [PHP](https://en.wikipedia.org/wiki/PHP) | Zlib | https://github.com/joseph-montanez/raylib-php   |
| raylib-phpcpp      | 3.5     | [PHP](https://en.wikipedia.org/wiki/PHP) | Zlib | https://github.com/oraoto/raylib-phpcpp         |
| raylibr            | **4.0** | [R](https://www.r-project.org)       | MIT | https://github.com/jeroenjanssens/raylibr         |
| raylib-rs          | 3.5     | [Rust](https://www.rust-lang.org/)   | Zlib | https://github.com/deltaphc/raylib-rs        |
| Relib              | 3.5     | [ReCT](https://github.com/RedCubeDev-ByteSpace/ReCT) | ?   | https://github.com/RedCubeDev-ByteSpace/Relib |
| racket-raylib      | **4.0** | [Racket](https://racket-lang.org/) | MIT/Apache-2.0 | https://github.com/eutro/racket-raylib |
| raylib-swift       | **4.0**     | [Swift](https://swift.org/)          | MIT | https://github.com/STREGAsGate/Raylib          |
| raylib-scopes      | auto    | [Scopes](http://scopes.rocks)          | MIT | https://github.com/salotz/raylib-scopes   |
| raylib-smallBasic  | 4.1-dev | [SmallBASIC](https://github.com/smallbasic/SmallBASIC) | GPLv3 | https://github.com/smallbasic/smallbasic.plugins/tree/master/raylib |
| raylib-umka        | **4.2** | [Umka](https://github.com/vtereshkov/umka-lang) | Zlib | https://github.com/robloach/raylib-umka |
| raylib.v           | **4.2** | [V](https://vlang.io/)               | Zlib     | https://github.com/irishgreencitrus/raylib.v |
| raylib-vapi        | **4.2** | [Vala](https://vala.dev/)            | Zlib     | https://github.com/lxmcf/raylib-vapi         |
| raylib-wren        | **4.0** | [Wren](http://wren.io/)              | ISC | https://github.com/TSnake41/raylib-wren              |
| raylib-zig         | **4.2** | [Zig](https://ziglang.org/)               | MIT | https://github.com/Not-Nik/raylib-zig     |
| raylib.zig         | **4.2** | [Zig](https://ziglang.org/)               | MIT | https://github.com/ryupold/raylib.zig |
| hare-raylib        | auto    | [Hare](https://harelang.org/)        | Zlib | https://git.sr.ht/~evantj/hare-raylib       |
| raylib-sunder      | auto    | [Sunder](https://github.com/ashn-dot-dev/sunder) | 0BSD | https://github.com/ashn-dot-dev/raylib-sunder |
| rayed-bqn          | auto    | [BQN](https://mlochbaum.github.io/BQN/) | MIT | https://github.com/Brian-ED/rayed-bqn | 


### Utility Wrapers
These are utility wrappers for specific languages, they are not required to use raylib in the language but may adapt the raylib API to be more inline with the language's pardigm.
|  name              | raylib version | language  | license | repo                                                         |
|:------------------:|:-------------: | :--------:|:-------:|:-------------------------------------------------------------|
| raylib-cpp         | **4.2** | [C++](https://en.wikipedia.org/wiki/C%2B%2B)  | Zlib | https://github.com/robloach/raylib-cpp  |
| claylib            | **4.2** | [Common Lisp](https://common-lisp.net/) | Zlib | https://github.com/defun-games/claylib |

### Older or Unmaintained Language Bindings
These are older raylib bindings that are more than 2 versions old or have not been maintained.
|  name              | raylib version | language  | repo                                                                 |
|:------------------:|:-------------: | :--------:|----------------------------------------------------------------------|
| raylib-cppsharp    | 2.5 | [C#](https://en.wikipedia.org/wiki/C_Sharp_(programming_language))       | https://github.com/phxvyper/raylib-cppsharp |
| RaylibFS           | 2.5 | [F#](https://fsharp.org/)             | https://github.com/dallinbeutler/RaylibFS     |
| raylib_d           | 2.5 | [D](https://dlang.org/)               | https://github.com/Sepheus/raylib_d     |
| bindbc-raylib      | 3.0 | [D](https://dlang.org/)               | https://github.com/o3o/bindbc-raylib   |
| go-raylib          | 3.5 | [Go](https://golang.org/)             | https://github.com/chunqian/go-raylib  |
| raylib-goplus      | 2.6-dev | [Go](https://golang.org/)         | https://github.com/Lachee/raylib-goplus         |
| ray-go             | 2.6-dev | [Go](https://golang.org/)         | https://github.com/hecate-tech/ray-go     |
| raylib-luamore     | 3.0 | [Lua](http://www.lua.org/)            | https://github.com/HDPLocust/raylib-luamore    |
| LuaJIT-Raylib      | 2.6 | [Lua](http://www.lua.org/)            | https://github.com/Bambofy/LuaJIT-Raylib    |
| raylib-lua-sol     | 2.5 | [Lua](http://www.lua.org/)            | https://github.com/RobLoach/raylib-lua-sol     |
| raylib-lua-ffi     | 2.0 | [Lua](http://www.lua.org/)            | https://github.com/raysan5/raylib/issues/693    |
| raylib-lua         | 1.7 | [Lua](http://www.lua.org/)            | https://github.com/raysan5/raylib-lua       |
| raylib-nelua       | 3.0 | [Nelua](https://nelua.io/)            | https://github.com/Andre-LA/raylib-nelua     |
| raylib-nim         | 2.0 | [Nim](https://nim-lang.org/)          | https://github.com/Skrylar/raylib-nim                  |
| raylib-Nim         | 1.7 | [Nim](https://nim-lang.org/)          | https://gitlab.com/define-private-public/raylib-Nim     |
| nim-raylib         | 3.1-dev | [Nim](https://nim-lang.org/)      | https://github.com/tomc1998/nim-raylib            |
| raylib-haskell     | 2.0 | [Haskell](https://www.haskell.org/)   | https://github.com/DevJac/raylib-haskell |
| raylib-cr          | 2.5-dev | [Crystal](https://crystal-lang.org/)   | https://github.com/AregevDev/raylib-cr      |
| raylib.cr          | 2.0 | [Crystal](https://crystal-lang.org/)  | https://github.com/sam0x17/raylib.cr     |
| cray               | 1.8 | [Crystal](https://crystal-lang.org/)  | https://gitlab.com/Zatherz/cray           |
| raylib-pas         | 3.0 | [Pascal](https://en.wikipedia.org/wiki/Pascal_(programming_language))         | https://github.com/tazdij/raylib-pas      |
| raylib-pascal      | 2.0 | [Pascal](https://en.wikipedia.org/wiki/Pascal_(programming_language))         | https://github.com/drezgames/raylib-pascal    |
| Graphics-Raylib    | 1.4 | [Perl](https://www.perl.org/)         | https://github.com/athreef/Graphics-Raylib      |
| raylib-ruby        | 2.6 | [Ruby](https://www.ruby-lang.org/en/) | https://github.com/a0/raylib-ruby        |
| raylib-ruby-ffi    | 2.0 | [Ruby](https://www.ruby-lang.org/en/) | https://github.com/D3nX/raylib-ruby-ffi      |
| raylib-mruby       | 2.5-dev | [mruby](https://github.com/mruby/mruby)  | https://github.com/lihaochen910/raylib-mruby    |
| raylib-java        | 2.0 | [Java](https://en.wikipedia.org/wiki/Java_(programming_language))           | https://github.com/XoanaIO/raylib-java   |
| clj-raylib         | 3.0 | [Clojure](https://clojure.org/)            | https://github.com/lsevero/clj-raylib       |
| QuickJS-raylib     | 3.0 | [QuickJS](https://bellard.org/quickjs/)    | https://github.com/sntg-p/QuickJS-raylib    |
| raylib-duktape     | 2.6 | [JavaScript (Duktape)](https://en.wikipedia.org/wiki/JavaScript) | https://github.com/RobLoach/raylib-duktape |
| raylib-v7          | 3.5 | [JavaScript (v7)](https://en.wikipedia.org/wiki/JavaScript)      | https://github.com/Rabios/raylib-v7        |
| raylib-chaiscript  | 2.6 | [ChaiScript](http://chaiscript.com/)       | https://github.com/RobLoach/raylib-chaiscript        |
| raylib-squirrel    | 2.5 | [Squirrel](http://www.squirrel-lang.org/)     | https://github.com/RobLoach/raylib-squirrel    |
| racket-raylib-2d   | 2.5 | [Racket](https://racket-lang.org/)         | https://github.com/arvyy/racket-raylib-2d           |
| raylib-php-ffi     | 2.4-dev | [PHP](https://en.wikipedia.org/wiki/PHP)      | https://github.com/oraoto/raylib-php-ffi      |
| raylib-haxe        | 2.4 | [Haxe](https://haxe.org/)                  | https://github.com/ibilon/raylib-haxe           |
| ringraylib         | 2.6 | [Ring](http://ring-lang.sourceforge.net/)  | https://github.com/ringpackages/ringraylib     |
| raylib-scm         | 2.5 | [Chicken Scheme](https://www.call-cc.org/) | https://github.com/yashrk/raylib-scm     |
| raylib-chibi       | 2.5 | [Chibi-Scheme](https://github.com/ashinn/chibi-scheme)   | https://github.com/VincentToups/raylib-chibi  |
| raylib-gambit-scheme | 3.1-dev | [Gambit Scheme](https://github.com/gambit/gambit)   | https://github.com/georgjz/raylib-gambit-scheme  |
| Euraylib           | 3.0 | [Euphoria](https://openeuphoria.org/)      | https://github.com/gAndy50/Euraylib          |
| raylib-odin        | 3.0 | [Odin](https://odin-lang.org/)             | https://github.com/kevinw/raylib-odin     |
| vraylib            | 3.5 | [V](https://vlang.io/)                     | https://github.com/waotzi/vraylib      |
| raylib-vala        | 3.0 | [Vala](https://wiki.gnome.org/Projects/Vala) | https://code.guddler.uk/mart/raylibVapi |
| raylib-jai         | 3.1-dev | [Jai](https://github.com/BSVino/JaiPrimer/blob/master/JaiPrimer.md)  | https://github.com/kujukuju/raylib-jai   |
| ray.zig            | 2.5 | [Zig](https://ziglang.org/)                | https://github.com/BitPuffin/zig-raylib-experiments |
| raylib-Ada         | 3.0 | [Ada](https://www.adacore.com/about-ada)   | https://github.com/mimo/raylib-Ada        |
| jaylib             | 3.0 | [Janet](https://janet-lang.org/)           | https://github.com/janet-lang/jaylib      |
| raykit             | ?   | [Kit](https://www.kitlang.org/)            | https://github.com/Gamerfiend/raykit      |
| ray.mod            | 3.0 | [BlitzMax](https://blitzmax.org/)          | https://github.com/bmx-ng/ray.mod         |
| raylib-mosaic      | 3.0 | [Mosaic](https://github.com/sal55/langs/tree/master/Mosaic)     | https://github.com/pluckyporcupine/raylib-mosaic   |
| raylib-xdpw        | 2.6 | [XD Pascal](https://github.com/vtereshkov/xdpw)     | https://github.com/vtereshkov/raylib-xdpw   |
| raylib-carp        | 3.0 | [Carp](https://github.com/carp-lang/Carp)           | https://github.com/pluckyporcupine/raylib-carp   |
| raylib-fb          | 3.0 | [FreeBasic](https://www.freebasic.net/)    | https://github.com/IchMagBier/raylib-fb  |
| raylib-purebasic   | 3.0 | [PureBasic](https://www.purebasic.com/)    | https://github.com/D-a-n-i-l-o/raylib-purebasic   |
| raylib-ats2        | 3.0 | [ATS2](http://www.ats-lang.org/)           | https://github.com/mephistopheles-8/raylib-ats2      |
| raylib-beef        | 3.0 | [Beef](https://www.beeflang.org/)          | https://github.com/M0n7y5/raylib-beef    |
| raylib-never       | 3.0 | [Never](https://github.com/never-lang/never) | https://github.com/never-lang/raylib-never |
| raylib.cbl         | 2.0 | [COBOL](https://en.wikipedia.org/wiki/COBOL) | *[code examples](https://github.com/Martinfx/Cobol/tree/master/OpenCobol/Games/raylib)* |


Missing some language or wrapper? Feel free to create a new one! :)

Usually, raylib bindings follow the convention: `raylib-{language}`

Let me know if you're writing a new binding for raylib, I will list it here!
