{
    "recommendations": [
        // Essential C++ Language Support
        "ms-vscode.cpptools-extension-pack",
        "ms-vscode.cpptools",
        "ms-vscode.cpptools-themes",
        
        // CMake Integration
        "ms-vscode.cmake-tools",
        "twxs.cmake",
        
        // Code Formatting and Quality
        "xaver.clang-format",
        "ms-vscode.vscode-clangd",
        
        // Git Integration (Enhanced)
        "eamodio.gitlens",
        
        // General Development Quality of Life
        "ms-vscode.hexeditor",
        "streetsidesoftware.code-spell-checker",
        "gruntfuggly.todo-tree",
        "ms-vscode.vscode-json",
        
        // Debugging and Performance
        "vadimcn.vscode-lldb",
        
        // Raylib/Game Development Specific
        "slevesque.shader",
        "ms-vscode.live-server"
    ],
    "unwantedRecommendations": [
        // Avoid conflicting extensions
        "ms-vscode.vscode-typescript"
    ]
}
