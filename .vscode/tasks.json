{"version": "2.0.0", "tasks": [{"label": "CMake: Configure", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-DCMAKE_BUILD_TYPE=Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "CMake: Build Debug", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "dependsOn": "CMake: Configure"}, {"label": "CMake: Build Release", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-DCMAKE_BUILD_TYPE=Release", "&&", "cmake", "--build", "build", "--config", "Release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"]}, {"label": "Run Application", "type": "shell", "command": "./build/RaylibApp", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "dependsOn": "CMake: Build Debug"}, {"label": "Clean Build", "type": "shell", "command": "rm", "args": ["-rf", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}