{"version": 4, "configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/include", "${workspaceFolder}/external/raylib/src", "${workspaceFolder}/src", "/usr/include/**", "/usr/local/include/**"], "defines": ["PLATFORM_DESKTOP", "_DEBUG"], "compilerPath": "/usr/bin/gcc", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64", "configurationProvider": "ms-vscode.cmake-tools", "compilerArgs": ["-Wall", "-Wextra", "-g"]}]}