{"version": "0.2.0", "configurations": [{"name": "Debug RaylibApp", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/RaylibApp", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: Build Debug", "miDebuggerPath": "/usr/bin/gdb", "logging": {"engineLogging": false}}, {"name": "Debug RaylibApp (LLDB)", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/build/RaylibApp", "args": [], "cwd": "${workspaceFolder}", "preLaunchTask": "CMake: Build Debug"}]}