#ifndef ENTITYMANAGER_H
#define ENTITYMANAGER_H

#include "raylib.h"
#include "Entity.h"
#include <vector>
#include <cstddef>

// Forward declaration to avoid circular dependency
class SoundManager;

/**
 * EntityManager class - manages multiple entities and handles selection logic
 *
 * This class demonstrates several important C++ concepts:
 * - Container usage (std::vector)
 * - Object composition (managing multiple Entity objects)
 * - Input handling and game state management
 * - Separation of concerns (entities vs. entity management)
 */
class EntityManager {
private:
    std::vector<Entity> entities;  // Container to hold all entities
    int selectedEntityIndex;       // Index of currently selected entity (-1 if none)
    SoundManager* soundManager;    // Pointer to sound manager for audio feedback

public:
    // Constructor - initializes the entity manager
    EntityManager();

    // Destructor
    ~EntityManager();

    // Entity management methods
    void AddEntity(const Entity& entity);
    void AddEntity(float x, float y, float size, Color color, float speed);

    // Selection methods
    void SelectEntity(int index);
    void SelectEntityByMouse(Camera2D camera);  // Now takes camera for coordinate conversion
    void SelectEntityByKeyboard(int keyNumber);  // 1 or 2 for first/second entity
    void DeselectAll();

    // Input handling
    void HandleInput(Camera2D camera);  // Now takes camera for coordinate conversion

    // Update and draw methods
    void Update();
    void DrawAll();

    // Utility methods
    int GetSelectedEntityIndex() const;
    bool HasSelectedEntity() const;
    Entity* GetSelectedEntity();  // Returns pointer to selected entity (or nullptr)
    size_t GetEntityCount() const;

    // Screen boundary management for all entities
    void ClampAllToBounds(int screenWidth, int screenHeight);

    // New methods for item interaction
    void AddItemEntity(float x, float y, float size, Color color);
    void AddShoesEntity(float x, float y, float size, Color color); // Add equipable shoes
    void CheckItemPickup(); // Check if selected entity can pick up nearby items
    void DropCarriedItem(); // Drop currently carried item

    // Equipment system methods
    void EquipCarriedItem(); // Equip a carried item if possible
    void UnequipItem();      // Unequip currently equipped item

    // Sound system integration
    void SetSoundManager(SoundManager* manager);
};

#endif // ENTITYMANAGER_H
