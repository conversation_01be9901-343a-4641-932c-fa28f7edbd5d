#ifndef ENTITY_H
#define ENTITY_H

#include "raylib.h"

/**
 * Entity class - represents a simple game object that can be drawn and moved
 *
 * This is a beginner-friendly class that demonstrates basic C++ concepts:
 * - Class definition with public and private members
 * - Constructor for initialization
 * - Member functions (methods) for behavior
 * - Encapsulation (private data with public interface)
 */
class Entity {
private:
    // Private member variables - these can only be accessed from within the class
    float x;        // X position on screen
    float y;        // Y position on screen
    float width;    // Width of the entity (square)
    float height;   // Height of the entity (square)
    Color color;    // Color of the entity
    Color originalColor;  // Store original color for selection feedback
    float speed;    // Movement speed in pixels per frame
    bool selected;  // Whether this entity is currently selected

    // New properties for item functionality
    bool isItem;           // Whether this entity is a pickup item
    bool isPickedUp;       // Whether this item has been picked up
    Entity* carrier;       // Pointer to the entity carrying this item
    float offsetX, offsetY; // Offset position when carried

public:
    // Constructor - called when creating a new Entity object
    // Takes initial position, size, color, and speed as parameters
    Entity(float startX, float startY, float size, Color entityColor, float moveSpeed,
           bool isPickupItem = false);

    // Destructor - called when the Entity object is destroyed
    // For this simple class, we don't need to do anything special
    ~Entity();

    // Update method - called every frame to update the entity's state
    // This is where we'll handle movement and other game logic
    void Update();

    // Draw method - called every frame to render the entity on screen
    // This uses Raylib's drawing functions to display the square
    void Draw();

    // Movement methods - these will move the entity in different directions
    void MoveUp();
    void MoveDown();
    void MoveLeft();
    void MoveRight();

    // Getter methods - these allow other code to read the entity's properties
    // In C++, it's good practice to keep data private and provide getters/setters
    float GetX() const;         // const means this method doesn't modify the object
    float GetY() const;
    float GetWidth() const;
    float GetHeight() const;
    Color GetColor() const;

    // Setter methods - these allow other code to modify the entity's properties
    void SetPosition(float newX, float newY);
    void SetColor(Color newColor);
    void SetSpeed(float newSpeed);

    // Utility method to check if the entity is within screen boundaries
    bool IsWithinBounds(int screenWidth, int screenHeight);

    // Method to keep the entity within screen boundaries
    void ClampToBounds(int screenWidth, int screenHeight);

    // Selection-related methods
    bool IsSelected() const;
    void SetSelected(bool isSelected);

    // Mouse collision detection
    bool IsPointInside(float pointX, float pointY) const;
    bool IsMouseOver() const;  // Convenience method using Raylib's GetMousePosition()

    // New methods for item functionality
    bool IsItem() const;
    bool IsPickedUp() const;
    void PickUp(Entity* newCarrier, float xOffset, float yOffset);
    void Drop();
    void UpdatePosition(); // Override to handle following carrier
};

#endif // ENTITY_H
