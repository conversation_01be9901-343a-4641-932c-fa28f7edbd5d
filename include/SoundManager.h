#ifndef SOUNDMANAGER_H
#define SOUNDMANAGER_H

#include "raylib.h"
#include <unordered_map>
#include <string>

/**
 * SoundManager class - Centralized audio management system
 * 
 * This class handles loading, storing, and playing sound effects for the game.
 * It provides a simple interface for playing sounds by name and manages
 * memory efficiently by loading sounds once and reusing them.
 * 
 * Features:
 * - Load sounds from files with automatic error handling
 * - Play sounds by name with volume control
 * - Prevent overlapping sounds when needed
 * - Automatic cleanup of loaded sounds
 * - Support for different sound categories (effects, footsteps, etc.)
 */
class SoundManager {
private:
    // Map to store loaded sounds by name for easy access
    std::unordered_map<std::string, Sound> sounds;
    
    // Global volume settings for different sound categories
    float masterVolume;
    float effectsVolume;
    float footstepsVolume;
    
    // Track if audio system is initialized
    bool audioInitialized;

public:
    // Constructor and destructor
    SoundManager();
    ~SoundManager();
    
    // Audio system management
    bool InitializeAudio();
    void CleanupAudio();
    
    // Sound loading and management
    bool LoadSound(const std::string& name, const std::string& filename);
    void UnloadSound(const std::string& name);
    void UnloadAllSounds();
    
    // Sound playback
    void PlaySound(const std::string& name);
    void PlaySound(const std::string& name, float volume);
    void PlaySoundIfNotPlaying(const std::string& name); // Prevents overlapping
    
    // Volume control
    void SetMasterVolume(float volume);
    void SetEffectsVolume(float volume);
    void SetFootstepsVolume(float volume);
    float GetMasterVolume() const;
    float GetEffectsVolume() const;
    float GetFootstepsVolume() const;
    
    // Utility methods
    bool IsSoundLoaded(const std::string& name) const;
    bool IsSoundPlaying(const std::string& name) const;
    bool IsAudioInitialized() const;
    
    // Predefined sound loading for game events
    void LoadGameSounds(); // Load all game-specific sounds
};

#endif // SOUNDMANAGER_H
