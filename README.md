# My First C++ Raylib Project

This is a beginner-friendly C++ project using the Raylib graphics library.

## Project Structure
```
cpp/
├── src/           # Source code files
├── include/       # Header files
├── build/         # Build output directory
├── external/      # External dependencies (Raylib)
├── CMakeLists.txt # Build configuration
└── README.md      # This file
```

## Building the Project
```bash
mkdir build
cd build
cmake ..
make
```

## Running the Application
```bash
./build/raylib_app
```

## Dependencies
- C++ compiler (g++)
- CMake
- Raylib (built from source)
