# C++ Raylib Project .gitignore

# Build directories and outputs
build/
Build/
BUILD/
cmake-build-*/
out/
bin/
lib/
dist/

# Compiled Object files
*.o
*.obj
*.ko
*.elf

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.so.*
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app
RaylibApp
raylib_app

# CMake generated files
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Raylib build artifacts (preserve source but exclude builds)
external/raylib/build/
external/raylib/Build/
external/raylib/cmake-build-*/

# Raylib examples and projects (these are built executables, not source)
external/raylib/examples/*
!external/raylib/examples/*.c
!external/raylib/examples/*.cpp
!external/raylib/examples/*.h
!external/raylib/examples/Makefile*
!external/raylib/examples/CMakeLists.txt
!external/raylib/examples/*/
external/raylib/examples/*/*.exe
external/raylib/examples/*/*.out
external/raylib/examples/*/build/

# Raylib projects directory (IDE-specific project files)
external/raylib/projects/

# IDE and Editor files
# Visual Studio Code
*.code-workspace

# CLion
.idea/
cmake-build-*/

# Visual Studio
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.suo
*.user
*.userosscache
*.sln.docstates
*.VC.db
*.VC.VC.opendb

# Qt Creator
CMakeLists.txt.user
CMakeLists.txt.user.*

# Xcode
*.xcodeproj/
*.xcworkspace/

# Code::Blocks
*.cbp
*.layout
*.depend

# Dev-C++
*.dev

# System files
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
.directory
*~

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.#*
\#*#

# Log files
*.log
*.logs

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Backup files
*.bak
*.backup
*.old
*.orig

# Profiling data
*.prof

# Memory dumps
*.dmp

# Core dumps
core
core.*

# GDB files
.gdb_history

# Valgrind output
*.valgrind

# Coverage files
*.gcov
*.gcda
*.gcno
coverage.info
coverage/

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Local environment files
.env
.env.local
.env.*.local

# Cache directories
.cache/
*.cache

# Node.js (if using any web tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class
*.egg-info/
.pytest_cache/

# Custom project-specific ignores
# Add any project-specific files or directories here
assets/temp/
screenshots/
recordings/
